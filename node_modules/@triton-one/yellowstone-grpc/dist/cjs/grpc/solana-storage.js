"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NumPartitions = exports.BlockHeight = exports.UnixTimestamp = exports.Rewards = exports.Reward = exports.ReturnData = exports.UiTokenAmount = exports.TokenBalance = exports.CompiledInstruction = exports.InnerInstruction = exports.InnerInstructions = exports.TransactionError = exports.TransactionStatusMeta = exports.MessageAddressTableLookup = exports.MessageHeader = exports.Message = exports.Transaction = exports.ConfirmedTransaction = exports.ConfirmedBlock = exports.rewardTypeToJSON = exports.rewardTypeFromJSON = exports.RewardType = exports.protobufPackage = void 0;
var long_1 = __importDefault(require("long"));
var minimal_1 = __importDefault(require("protobufjs/minimal"));
exports.protobufPackage = "solana.storage.ConfirmedBlock";
var RewardType;
(function (RewardType) {
    RewardType[RewardType["Unspecified"] = 0] = "Unspecified";
    RewardType[RewardType["Fee"] = 1] = "Fee";
    RewardType[RewardType["Rent"] = 2] = "Rent";
    RewardType[RewardType["Staking"] = 3] = "Staking";
    RewardType[RewardType["Voting"] = 4] = "Voting";
    RewardType[RewardType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(RewardType || (exports.RewardType = RewardType = {}));
function rewardTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "Unspecified":
            return RewardType.Unspecified;
        case 1:
        case "Fee":
            return RewardType.Fee;
        case 2:
        case "Rent":
            return RewardType.Rent;
        case 3:
        case "Staking":
            return RewardType.Staking;
        case 4:
        case "Voting":
            return RewardType.Voting;
        case -1:
        case "UNRECOGNIZED":
        default:
            return RewardType.UNRECOGNIZED;
    }
}
exports.rewardTypeFromJSON = rewardTypeFromJSON;
function rewardTypeToJSON(object) {
    switch (object) {
        case RewardType.Unspecified:
            return "Unspecified";
        case RewardType.Fee:
            return "Fee";
        case RewardType.Rent:
            return "Rent";
        case RewardType.Staking:
            return "Staking";
        case RewardType.Voting:
            return "Voting";
        case RewardType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
exports.rewardTypeToJSON = rewardTypeToJSON;
function createBaseConfirmedBlock() {
    return {
        previousBlockhash: "",
        blockhash: "",
        parentSlot: "0",
        transactions: [],
        rewards: [],
        blockTime: undefined,
        blockHeight: undefined,
        numPartitions: undefined,
    };
}
exports.ConfirmedBlock = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.previousBlockhash !== "") {
            writer.uint32(10).string(message.previousBlockhash);
        }
        if (message.blockhash !== "") {
            writer.uint32(18).string(message.blockhash);
        }
        if (message.parentSlot !== "0") {
            writer.uint32(24).uint64(message.parentSlot);
        }
        for (var _i = 0, _a = message.transactions; _i < _a.length; _i++) {
            var v = _a[_i];
            exports.ConfirmedTransaction.encode(v, writer.uint32(34).fork()).ldelim();
        }
        for (var _b = 0, _c = message.rewards; _b < _c.length; _b++) {
            var v = _c[_b];
            exports.Reward.encode(v, writer.uint32(42).fork()).ldelim();
        }
        if (message.blockTime !== undefined) {
            exports.UnixTimestamp.encode(message.blockTime, writer.uint32(50).fork()).ldelim();
        }
        if (message.blockHeight !== undefined) {
            exports.BlockHeight.encode(message.blockHeight, writer.uint32(58).fork()).ldelim();
        }
        if (message.numPartitions !== undefined) {
            exports.NumPartitions.encode(message.numPartitions, writer.uint32(66).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseConfirmedBlock();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.previousBlockhash = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.blockhash = reader.string();
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.parentSlot = longToString(reader.uint64());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.transactions.push(exports.ConfirmedTransaction.decode(reader, reader.uint32()));
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.rewards.push(exports.Reward.decode(reader, reader.uint32()));
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.blockTime = exports.UnixTimestamp.decode(reader, reader.uint32());
                    continue;
                case 7:
                    if (tag !== 58) {
                        break;
                    }
                    message.blockHeight = exports.BlockHeight.decode(reader, reader.uint32());
                    continue;
                case 8:
                    if (tag !== 66) {
                        break;
                    }
                    message.numPartitions = exports.NumPartitions.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            previousBlockhash: isSet(object.previousBlockhash) ? globalThis.String(object.previousBlockhash) : "",
            blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
            parentSlot: isSet(object.parentSlot) ? globalThis.String(object.parentSlot) : "0",
            transactions: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.transactions)
                ? object.transactions.map(function (e) { return exports.ConfirmedTransaction.fromJSON(e); })
                : [],
            rewards: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.rewards) ? object.rewards.map(function (e) { return exports.Reward.fromJSON(e); }) : [],
            blockTime: isSet(object.blockTime) ? exports.UnixTimestamp.fromJSON(object.blockTime) : undefined,
            blockHeight: isSet(object.blockHeight) ? exports.BlockHeight.fromJSON(object.blockHeight) : undefined,
            numPartitions: isSet(object.numPartitions) ? exports.NumPartitions.fromJSON(object.numPartitions) : undefined,
        };
    },
    toJSON: function (message) {
        var _a, _b;
        var obj = {};
        if (message.previousBlockhash !== "") {
            obj.previousBlockhash = message.previousBlockhash;
        }
        if (message.blockhash !== "") {
            obj.blockhash = message.blockhash;
        }
        if (message.parentSlot !== "0") {
            obj.parentSlot = message.parentSlot;
        }
        if ((_a = message.transactions) === null || _a === void 0 ? void 0 : _a.length) {
            obj.transactions = message.transactions.map(function (e) { return exports.ConfirmedTransaction.toJSON(e); });
        }
        if ((_b = message.rewards) === null || _b === void 0 ? void 0 : _b.length) {
            obj.rewards = message.rewards.map(function (e) { return exports.Reward.toJSON(e); });
        }
        if (message.blockTime !== undefined) {
            obj.blockTime = exports.UnixTimestamp.toJSON(message.blockTime);
        }
        if (message.blockHeight !== undefined) {
            obj.blockHeight = exports.BlockHeight.toJSON(message.blockHeight);
        }
        if (message.numPartitions !== undefined) {
            obj.numPartitions = exports.NumPartitions.toJSON(message.numPartitions);
        }
        return obj;
    },
    create: function (base) {
        return exports.ConfirmedBlock.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseConfirmedBlock();
        message.previousBlockhash = (_a = object.previousBlockhash) !== null && _a !== void 0 ? _a : "";
        message.blockhash = (_b = object.blockhash) !== null && _b !== void 0 ? _b : "";
        message.parentSlot = (_c = object.parentSlot) !== null && _c !== void 0 ? _c : "0";
        message.transactions = ((_d = object.transactions) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.ConfirmedTransaction.fromPartial(e); })) || [];
        message.rewards = ((_e = object.rewards) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.Reward.fromPartial(e); })) || [];
        message.blockTime = (object.blockTime !== undefined && object.blockTime !== null)
            ? exports.UnixTimestamp.fromPartial(object.blockTime)
            : undefined;
        message.blockHeight = (object.blockHeight !== undefined && object.blockHeight !== null)
            ? exports.BlockHeight.fromPartial(object.blockHeight)
            : undefined;
        message.numPartitions = (object.numPartitions !== undefined && object.numPartitions !== null)
            ? exports.NumPartitions.fromPartial(object.numPartitions)
            : undefined;
        return message;
    },
};
function createBaseConfirmedTransaction() {
    return { transaction: undefined, meta: undefined };
}
exports.ConfirmedTransaction = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.transaction !== undefined) {
            exports.Transaction.encode(message.transaction, writer.uint32(10).fork()).ldelim();
        }
        if (message.meta !== undefined) {
            exports.TransactionStatusMeta.encode(message.meta, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseConfirmedTransaction();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.transaction = exports.Transaction.decode(reader, reader.uint32());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.meta = exports.TransactionStatusMeta.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            transaction: isSet(object.transaction) ? exports.Transaction.fromJSON(object.transaction) : undefined,
            meta: isSet(object.meta) ? exports.TransactionStatusMeta.fromJSON(object.meta) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.transaction !== undefined) {
            obj.transaction = exports.Transaction.toJSON(message.transaction);
        }
        if (message.meta !== undefined) {
            obj.meta = exports.TransactionStatusMeta.toJSON(message.meta);
        }
        return obj;
    },
    create: function (base) {
        return exports.ConfirmedTransaction.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseConfirmedTransaction();
        message.transaction = (object.transaction !== undefined && object.transaction !== null)
            ? exports.Transaction.fromPartial(object.transaction)
            : undefined;
        message.meta = (object.meta !== undefined && object.meta !== null)
            ? exports.TransactionStatusMeta.fromPartial(object.meta)
            : undefined;
        return message;
    },
};
function createBaseTransaction() {
    return { signatures: [], message: undefined };
}
exports.Transaction = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        for (var _i = 0, _a = message.signatures; _i < _a.length; _i++) {
            var v = _a[_i];
            writer.uint32(10).bytes(v);
        }
        if (message.message !== undefined) {
            exports.Message.encode(message.message, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTransaction();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.signatures.push(reader.bytes());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.message = exports.Message.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            signatures: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.signatures)
                ? object.signatures.map(function (e) { return bytesFromBase64(e); })
                : [],
            message: isSet(object.message) ? exports.Message.fromJSON(object.message) : undefined,
        };
    },
    toJSON: function (message) {
        var _a;
        var obj = {};
        if ((_a = message.signatures) === null || _a === void 0 ? void 0 : _a.length) {
            obj.signatures = message.signatures.map(function (e) { return base64FromBytes(e); });
        }
        if (message.message !== undefined) {
            obj.message = exports.Message.toJSON(message.message);
        }
        return obj;
    },
    create: function (base) {
        return exports.Transaction.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseTransaction();
        message.signatures = ((_a = object.signatures) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.message = (object.message !== undefined && object.message !== null)
            ? exports.Message.fromPartial(object.message)
            : undefined;
        return message;
    },
};
function createBaseMessage() {
    return {
        header: undefined,
        accountKeys: [],
        recentBlockhash: new Uint8Array(0),
        instructions: [],
        versioned: false,
        addressTableLookups: [],
    };
}
exports.Message = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.header !== undefined) {
            exports.MessageHeader.encode(message.header, writer.uint32(10).fork()).ldelim();
        }
        for (var _i = 0, _a = message.accountKeys; _i < _a.length; _i++) {
            var v = _a[_i];
            writer.uint32(18).bytes(v);
        }
        if (message.recentBlockhash.length !== 0) {
            writer.uint32(26).bytes(message.recentBlockhash);
        }
        for (var _b = 0, _c = message.instructions; _b < _c.length; _b++) {
            var v = _c[_b];
            exports.CompiledInstruction.encode(v, writer.uint32(34).fork()).ldelim();
        }
        if (message.versioned !== false) {
            writer.uint32(40).bool(message.versioned);
        }
        for (var _d = 0, _e = message.addressTableLookups; _d < _e.length; _d++) {
            var v = _e[_d];
            exports.MessageAddressTableLookup.encode(v, writer.uint32(50).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMessage();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.header = exports.MessageHeader.decode(reader, reader.uint32());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.accountKeys.push(reader.bytes());
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.recentBlockhash = reader.bytes();
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.instructions.push(exports.CompiledInstruction.decode(reader, reader.uint32()));
                    continue;
                case 5:
                    if (tag !== 40) {
                        break;
                    }
                    message.versioned = reader.bool();
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.addressTableLookups.push(exports.MessageAddressTableLookup.decode(reader, reader.uint32()));
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            header: isSet(object.header) ? exports.MessageHeader.fromJSON(object.header) : undefined,
            accountKeys: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.accountKeys)
                ? object.accountKeys.map(function (e) { return bytesFromBase64(e); })
                : [],
            recentBlockhash: isSet(object.recentBlockhash) ? bytesFromBase64(object.recentBlockhash) : new Uint8Array(0),
            instructions: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.instructions)
                ? object.instructions.map(function (e) { return exports.CompiledInstruction.fromJSON(e); })
                : [],
            versioned: isSet(object.versioned) ? globalThis.Boolean(object.versioned) : false,
            addressTableLookups: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.addressTableLookups)
                ? object.addressTableLookups.map(function (e) { return exports.MessageAddressTableLookup.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var _a, _b, _c;
        var obj = {};
        if (message.header !== undefined) {
            obj.header = exports.MessageHeader.toJSON(message.header);
        }
        if ((_a = message.accountKeys) === null || _a === void 0 ? void 0 : _a.length) {
            obj.accountKeys = message.accountKeys.map(function (e) { return base64FromBytes(e); });
        }
        if (message.recentBlockhash.length !== 0) {
            obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
        }
        if ((_b = message.instructions) === null || _b === void 0 ? void 0 : _b.length) {
            obj.instructions = message.instructions.map(function (e) { return exports.CompiledInstruction.toJSON(e); });
        }
        if (message.versioned !== false) {
            obj.versioned = message.versioned;
        }
        if ((_c = message.addressTableLookups) === null || _c === void 0 ? void 0 : _c.length) {
            obj.addressTableLookups = message.addressTableLookups.map(function (e) { return exports.MessageAddressTableLookup.toJSON(e); });
        }
        return obj;
    },
    create: function (base) {
        return exports.Message.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMessage();
        message.header = (object.header !== undefined && object.header !== null)
            ? exports.MessageHeader.fromPartial(object.header)
            : undefined;
        message.accountKeys = ((_a = object.accountKeys) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.recentBlockhash = (_b = object.recentBlockhash) !== null && _b !== void 0 ? _b : new Uint8Array(0);
        message.instructions = ((_c = object.instructions) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.CompiledInstruction.fromPartial(e); })) || [];
        message.versioned = (_d = object.versioned) !== null && _d !== void 0 ? _d : false;
        message.addressTableLookups = ((_e = object.addressTableLookups) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.MessageAddressTableLookup.fromPartial(e); })) ||
            [];
        return message;
    },
};
function createBaseMessageHeader() {
    return { numRequiredSignatures: 0, numReadonlySignedAccounts: 0, numReadonlyUnsignedAccounts: 0 };
}
exports.MessageHeader = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.numRequiredSignatures !== 0) {
            writer.uint32(8).uint32(message.numRequiredSignatures);
        }
        if (message.numReadonlySignedAccounts !== 0) {
            writer.uint32(16).uint32(message.numReadonlySignedAccounts);
        }
        if (message.numReadonlyUnsignedAccounts !== 0) {
            writer.uint32(24).uint32(message.numReadonlyUnsignedAccounts);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMessageHeader();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.numRequiredSignatures = reader.uint32();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.numReadonlySignedAccounts = reader.uint32();
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.numReadonlyUnsignedAccounts = reader.uint32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            numRequiredSignatures: isSet(object.numRequiredSignatures) ? globalThis.Number(object.numRequiredSignatures) : 0,
            numReadonlySignedAccounts: isSet(object.numReadonlySignedAccounts)
                ? globalThis.Number(object.numReadonlySignedAccounts)
                : 0,
            numReadonlyUnsignedAccounts: isSet(object.numReadonlyUnsignedAccounts)
                ? globalThis.Number(object.numReadonlyUnsignedAccounts)
                : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.numRequiredSignatures !== 0) {
            obj.numRequiredSignatures = Math.round(message.numRequiredSignatures);
        }
        if (message.numReadonlySignedAccounts !== 0) {
            obj.numReadonlySignedAccounts = Math.round(message.numReadonlySignedAccounts);
        }
        if (message.numReadonlyUnsignedAccounts !== 0) {
            obj.numReadonlyUnsignedAccounts = Math.round(message.numReadonlyUnsignedAccounts);
        }
        return obj;
    },
    create: function (base) {
        return exports.MessageHeader.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMessageHeader();
        message.numRequiredSignatures = (_a = object.numRequiredSignatures) !== null && _a !== void 0 ? _a : 0;
        message.numReadonlySignedAccounts = (_b = object.numReadonlySignedAccounts) !== null && _b !== void 0 ? _b : 0;
        message.numReadonlyUnsignedAccounts = (_c = object.numReadonlyUnsignedAccounts) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseMessageAddressTableLookup() {
    return { accountKey: new Uint8Array(0), writableIndexes: new Uint8Array(0), readonlyIndexes: new Uint8Array(0) };
}
exports.MessageAddressTableLookup = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.accountKey.length !== 0) {
            writer.uint32(10).bytes(message.accountKey);
        }
        if (message.writableIndexes.length !== 0) {
            writer.uint32(18).bytes(message.writableIndexes);
        }
        if (message.readonlyIndexes.length !== 0) {
            writer.uint32(26).bytes(message.readonlyIndexes);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMessageAddressTableLookup();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.accountKey = reader.bytes();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.writableIndexes = reader.bytes();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.readonlyIndexes = reader.bytes();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accountKey: isSet(object.accountKey) ? bytesFromBase64(object.accountKey) : new Uint8Array(0),
            writableIndexes: isSet(object.writableIndexes) ? bytesFromBase64(object.writableIndexes) : new Uint8Array(0),
            readonlyIndexes: isSet(object.readonlyIndexes) ? bytesFromBase64(object.readonlyIndexes) : new Uint8Array(0),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accountKey.length !== 0) {
            obj.accountKey = base64FromBytes(message.accountKey);
        }
        if (message.writableIndexes.length !== 0) {
            obj.writableIndexes = base64FromBytes(message.writableIndexes);
        }
        if (message.readonlyIndexes.length !== 0) {
            obj.readonlyIndexes = base64FromBytes(message.readonlyIndexes);
        }
        return obj;
    },
    create: function (base) {
        return exports.MessageAddressTableLookup.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMessageAddressTableLookup();
        message.accountKey = (_a = object.accountKey) !== null && _a !== void 0 ? _a : new Uint8Array(0);
        message.writableIndexes = (_b = object.writableIndexes) !== null && _b !== void 0 ? _b : new Uint8Array(0);
        message.readonlyIndexes = (_c = object.readonlyIndexes) !== null && _c !== void 0 ? _c : new Uint8Array(0);
        return message;
    },
};
function createBaseTransactionStatusMeta() {
    return {
        err: undefined,
        fee: "0",
        preBalances: [],
        postBalances: [],
        innerInstructions: [],
        innerInstructionsNone: false,
        logMessages: [],
        logMessagesNone: false,
        preTokenBalances: [],
        postTokenBalances: [],
        rewards: [],
        loadedWritableAddresses: [],
        loadedReadonlyAddresses: [],
        returnData: undefined,
        returnDataNone: false,
        computeUnitsConsumed: undefined,
    };
}
exports.TransactionStatusMeta = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.err !== undefined) {
            exports.TransactionError.encode(message.err, writer.uint32(10).fork()).ldelim();
        }
        if (message.fee !== "0") {
            writer.uint32(16).uint64(message.fee);
        }
        writer.uint32(26).fork();
        for (var _i = 0, _a = message.preBalances; _i < _a.length; _i++) {
            var v = _a[_i];
            writer.uint64(v);
        }
        writer.ldelim();
        writer.uint32(34).fork();
        for (var _b = 0, _c = message.postBalances; _b < _c.length; _b++) {
            var v = _c[_b];
            writer.uint64(v);
        }
        writer.ldelim();
        for (var _d = 0, _e = message.innerInstructions; _d < _e.length; _d++) {
            var v = _e[_d];
            exports.InnerInstructions.encode(v, writer.uint32(42).fork()).ldelim();
        }
        if (message.innerInstructionsNone !== false) {
            writer.uint32(80).bool(message.innerInstructionsNone);
        }
        for (var _f = 0, _g = message.logMessages; _f < _g.length; _f++) {
            var v = _g[_f];
            writer.uint32(50).string(v);
        }
        if (message.logMessagesNone !== false) {
            writer.uint32(88).bool(message.logMessagesNone);
        }
        for (var _h = 0, _j = message.preTokenBalances; _h < _j.length; _h++) {
            var v = _j[_h];
            exports.TokenBalance.encode(v, writer.uint32(58).fork()).ldelim();
        }
        for (var _k = 0, _l = message.postTokenBalances; _k < _l.length; _k++) {
            var v = _l[_k];
            exports.TokenBalance.encode(v, writer.uint32(66).fork()).ldelim();
        }
        for (var _m = 0, _o = message.rewards; _m < _o.length; _m++) {
            var v = _o[_m];
            exports.Reward.encode(v, writer.uint32(74).fork()).ldelim();
        }
        for (var _p = 0, _q = message.loadedWritableAddresses; _p < _q.length; _p++) {
            var v = _q[_p];
            writer.uint32(98).bytes(v);
        }
        for (var _r = 0, _s = message.loadedReadonlyAddresses; _r < _s.length; _r++) {
            var v = _s[_r];
            writer.uint32(106).bytes(v);
        }
        if (message.returnData !== undefined) {
            exports.ReturnData.encode(message.returnData, writer.uint32(114).fork()).ldelim();
        }
        if (message.returnDataNone !== false) {
            writer.uint32(120).bool(message.returnDataNone);
        }
        if (message.computeUnitsConsumed !== undefined) {
            writer.uint32(128).uint64(message.computeUnitsConsumed);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTransactionStatusMeta();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.err = exports.TransactionError.decode(reader, reader.uint32());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.fee = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag === 24) {
                        message.preBalances.push(longToString(reader.uint64()));
                        continue;
                    }
                    if (tag === 26) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.preBalances.push(longToString(reader.uint64()));
                        }
                        continue;
                    }
                    break;
                case 4:
                    if (tag === 32) {
                        message.postBalances.push(longToString(reader.uint64()));
                        continue;
                    }
                    if (tag === 34) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.postBalances.push(longToString(reader.uint64()));
                        }
                        continue;
                    }
                    break;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.innerInstructions.push(exports.InnerInstructions.decode(reader, reader.uint32()));
                    continue;
                case 10:
                    if (tag !== 80) {
                        break;
                    }
                    message.innerInstructionsNone = reader.bool();
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.logMessages.push(reader.string());
                    continue;
                case 11:
                    if (tag !== 88) {
                        break;
                    }
                    message.logMessagesNone = reader.bool();
                    continue;
                case 7:
                    if (tag !== 58) {
                        break;
                    }
                    message.preTokenBalances.push(exports.TokenBalance.decode(reader, reader.uint32()));
                    continue;
                case 8:
                    if (tag !== 66) {
                        break;
                    }
                    message.postTokenBalances.push(exports.TokenBalance.decode(reader, reader.uint32()));
                    continue;
                case 9:
                    if (tag !== 74) {
                        break;
                    }
                    message.rewards.push(exports.Reward.decode(reader, reader.uint32()));
                    continue;
                case 12:
                    if (tag !== 98) {
                        break;
                    }
                    message.loadedWritableAddresses.push(reader.bytes());
                    continue;
                case 13:
                    if (tag !== 106) {
                        break;
                    }
                    message.loadedReadonlyAddresses.push(reader.bytes());
                    continue;
                case 14:
                    if (tag !== 114) {
                        break;
                    }
                    message.returnData = exports.ReturnData.decode(reader, reader.uint32());
                    continue;
                case 15:
                    if (tag !== 120) {
                        break;
                    }
                    message.returnDataNone = reader.bool();
                    continue;
                case 16:
                    if (tag !== 128) {
                        break;
                    }
                    message.computeUnitsConsumed = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            err: isSet(object.err) ? exports.TransactionError.fromJSON(object.err) : undefined,
            fee: isSet(object.fee) ? globalThis.String(object.fee) : "0",
            preBalances: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.preBalances)
                ? object.preBalances.map(function (e) { return globalThis.String(e); })
                : [],
            postBalances: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.postBalances)
                ? object.postBalances.map(function (e) { return globalThis.String(e); })
                : [],
            innerInstructions: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.innerInstructions)
                ? object.innerInstructions.map(function (e) { return exports.InnerInstructions.fromJSON(e); })
                : [],
            innerInstructionsNone: isSet(object.innerInstructionsNone)
                ? globalThis.Boolean(object.innerInstructionsNone)
                : false,
            logMessages: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.logMessages)
                ? object.logMessages.map(function (e) { return globalThis.String(e); })
                : [],
            logMessagesNone: isSet(object.logMessagesNone) ? globalThis.Boolean(object.logMessagesNone) : false,
            preTokenBalances: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.preTokenBalances)
                ? object.preTokenBalances.map(function (e) { return exports.TokenBalance.fromJSON(e); })
                : [],
            postTokenBalances: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.postTokenBalances)
                ? object.postTokenBalances.map(function (e) { return exports.TokenBalance.fromJSON(e); })
                : [],
            rewards: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.rewards) ? object.rewards.map(function (e) { return exports.Reward.fromJSON(e); }) : [],
            loadedWritableAddresses: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.loadedWritableAddresses)
                ? object.loadedWritableAddresses.map(function (e) { return bytesFromBase64(e); })
                : [],
            loadedReadonlyAddresses: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.loadedReadonlyAddresses)
                ? object.loadedReadonlyAddresses.map(function (e) { return bytesFromBase64(e); })
                : [],
            returnData: isSet(object.returnData) ? exports.ReturnData.fromJSON(object.returnData) : undefined,
            returnDataNone: isSet(object.returnDataNone) ? globalThis.Boolean(object.returnDataNone) : false,
            computeUnitsConsumed: isSet(object.computeUnitsConsumed)
                ? globalThis.String(object.computeUnitsConsumed)
                : undefined,
        };
    },
    toJSON: function (message) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var obj = {};
        if (message.err !== undefined) {
            obj.err = exports.TransactionError.toJSON(message.err);
        }
        if (message.fee !== "0") {
            obj.fee = message.fee;
        }
        if ((_a = message.preBalances) === null || _a === void 0 ? void 0 : _a.length) {
            obj.preBalances = message.preBalances;
        }
        if ((_b = message.postBalances) === null || _b === void 0 ? void 0 : _b.length) {
            obj.postBalances = message.postBalances;
        }
        if ((_c = message.innerInstructions) === null || _c === void 0 ? void 0 : _c.length) {
            obj.innerInstructions = message.innerInstructions.map(function (e) { return exports.InnerInstructions.toJSON(e); });
        }
        if (message.innerInstructionsNone !== false) {
            obj.innerInstructionsNone = message.innerInstructionsNone;
        }
        if ((_d = message.logMessages) === null || _d === void 0 ? void 0 : _d.length) {
            obj.logMessages = message.logMessages;
        }
        if (message.logMessagesNone !== false) {
            obj.logMessagesNone = message.logMessagesNone;
        }
        if ((_e = message.preTokenBalances) === null || _e === void 0 ? void 0 : _e.length) {
            obj.preTokenBalances = message.preTokenBalances.map(function (e) { return exports.TokenBalance.toJSON(e); });
        }
        if ((_f = message.postTokenBalances) === null || _f === void 0 ? void 0 : _f.length) {
            obj.postTokenBalances = message.postTokenBalances.map(function (e) { return exports.TokenBalance.toJSON(e); });
        }
        if ((_g = message.rewards) === null || _g === void 0 ? void 0 : _g.length) {
            obj.rewards = message.rewards.map(function (e) { return exports.Reward.toJSON(e); });
        }
        if ((_h = message.loadedWritableAddresses) === null || _h === void 0 ? void 0 : _h.length) {
            obj.loadedWritableAddresses = message.loadedWritableAddresses.map(function (e) { return base64FromBytes(e); });
        }
        if ((_j = message.loadedReadonlyAddresses) === null || _j === void 0 ? void 0 : _j.length) {
            obj.loadedReadonlyAddresses = message.loadedReadonlyAddresses.map(function (e) { return base64FromBytes(e); });
        }
        if (message.returnData !== undefined) {
            obj.returnData = exports.ReturnData.toJSON(message.returnData);
        }
        if (message.returnDataNone !== false) {
            obj.returnDataNone = message.returnDataNone;
        }
        if (message.computeUnitsConsumed !== undefined) {
            obj.computeUnitsConsumed = message.computeUnitsConsumed;
        }
        return obj;
    },
    create: function (base) {
        return exports.TransactionStatusMeta.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        var message = createBaseTransactionStatusMeta();
        message.err = (object.err !== undefined && object.err !== null)
            ? exports.TransactionError.fromPartial(object.err)
            : undefined;
        message.fee = (_a = object.fee) !== null && _a !== void 0 ? _a : "0";
        message.preBalances = ((_b = object.preBalances) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.postBalances = ((_c = object.postBalances) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.innerInstructions = ((_d = object.innerInstructions) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.InnerInstructions.fromPartial(e); })) || [];
        message.innerInstructionsNone = (_e = object.innerInstructionsNone) !== null && _e !== void 0 ? _e : false;
        message.logMessages = ((_f = object.logMessages) === null || _f === void 0 ? void 0 : _f.map(function (e) { return e; })) || [];
        message.logMessagesNone = (_g = object.logMessagesNone) !== null && _g !== void 0 ? _g : false;
        message.preTokenBalances = ((_h = object.preTokenBalances) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exports.TokenBalance.fromPartial(e); })) || [];
        message.postTokenBalances = ((_j = object.postTokenBalances) === null || _j === void 0 ? void 0 : _j.map(function (e) { return exports.TokenBalance.fromPartial(e); })) || [];
        message.rewards = ((_k = object.rewards) === null || _k === void 0 ? void 0 : _k.map(function (e) { return exports.Reward.fromPartial(e); })) || [];
        message.loadedWritableAddresses = ((_l = object.loadedWritableAddresses) === null || _l === void 0 ? void 0 : _l.map(function (e) { return e; })) || [];
        message.loadedReadonlyAddresses = ((_m = object.loadedReadonlyAddresses) === null || _m === void 0 ? void 0 : _m.map(function (e) { return e; })) || [];
        message.returnData = (object.returnData !== undefined && object.returnData !== null)
            ? exports.ReturnData.fromPartial(object.returnData)
            : undefined;
        message.returnDataNone = (_o = object.returnDataNone) !== null && _o !== void 0 ? _o : false;
        message.computeUnitsConsumed = (_p = object.computeUnitsConsumed) !== null && _p !== void 0 ? _p : undefined;
        return message;
    },
};
function createBaseTransactionError() {
    return { err: new Uint8Array(0) };
}
exports.TransactionError = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.err.length !== 0) {
            writer.uint32(10).bytes(message.err);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTransactionError();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.err = reader.bytes();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return { err: isSet(object.err) ? bytesFromBase64(object.err) : new Uint8Array(0) };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.err.length !== 0) {
            obj.err = base64FromBytes(message.err);
        }
        return obj;
    },
    create: function (base) {
        return exports.TransactionError.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseTransactionError();
        message.err = (_a = object.err) !== null && _a !== void 0 ? _a : new Uint8Array(0);
        return message;
    },
};
function createBaseInnerInstructions() {
    return { index: 0, instructions: [] };
}
exports.InnerInstructions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.index !== 0) {
            writer.uint32(8).uint32(message.index);
        }
        for (var _i = 0, _a = message.instructions; _i < _a.length; _i++) {
            var v = _a[_i];
            exports.InnerInstruction.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInnerInstructions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.index = reader.uint32();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.instructions.push(exports.InnerInstruction.decode(reader, reader.uint32()));
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            index: isSet(object.index) ? globalThis.Number(object.index) : 0,
            instructions: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.instructions)
                ? object.instructions.map(function (e) { return exports.InnerInstruction.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var _a;
        var obj = {};
        if (message.index !== 0) {
            obj.index = Math.round(message.index);
        }
        if ((_a = message.instructions) === null || _a === void 0 ? void 0 : _a.length) {
            obj.instructions = message.instructions.map(function (e) { return exports.InnerInstruction.toJSON(e); });
        }
        return obj;
    },
    create: function (base) {
        return exports.InnerInstructions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseInnerInstructions();
        message.index = (_a = object.index) !== null && _a !== void 0 ? _a : 0;
        message.instructions = ((_b = object.instructions) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.InnerInstruction.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseInnerInstruction() {
    return { programIdIndex: 0, accounts: new Uint8Array(0), data: new Uint8Array(0), stackHeight: undefined };
}
exports.InnerInstruction = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.programIdIndex !== 0) {
            writer.uint32(8).uint32(message.programIdIndex);
        }
        if (message.accounts.length !== 0) {
            writer.uint32(18).bytes(message.accounts);
        }
        if (message.data.length !== 0) {
            writer.uint32(26).bytes(message.data);
        }
        if (message.stackHeight !== undefined) {
            writer.uint32(32).uint32(message.stackHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInnerInstruction();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.programIdIndex = reader.uint32();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.accounts = reader.bytes();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.data = reader.bytes();
                    continue;
                case 4:
                    if (tag !== 32) {
                        break;
                    }
                    message.stackHeight = reader.uint32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
            accounts: isSet(object.accounts) ? bytesFromBase64(object.accounts) : new Uint8Array(0),
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0),
            stackHeight: isSet(object.stackHeight) ? globalThis.Number(object.stackHeight) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.programIdIndex !== 0) {
            obj.programIdIndex = Math.round(message.programIdIndex);
        }
        if (message.accounts.length !== 0) {
            obj.accounts = base64FromBytes(message.accounts);
        }
        if (message.data.length !== 0) {
            obj.data = base64FromBytes(message.data);
        }
        if (message.stackHeight !== undefined) {
            obj.stackHeight = Math.round(message.stackHeight);
        }
        return obj;
    },
    create: function (base) {
        return exports.InnerInstruction.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseInnerInstruction();
        message.programIdIndex = (_a = object.programIdIndex) !== null && _a !== void 0 ? _a : 0;
        message.accounts = (_b = object.accounts) !== null && _b !== void 0 ? _b : new Uint8Array(0);
        message.data = (_c = object.data) !== null && _c !== void 0 ? _c : new Uint8Array(0);
        message.stackHeight = (_d = object.stackHeight) !== null && _d !== void 0 ? _d : undefined;
        return message;
    },
};
function createBaseCompiledInstruction() {
    return { programIdIndex: 0, accounts: new Uint8Array(0), data: new Uint8Array(0) };
}
exports.CompiledInstruction = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.programIdIndex !== 0) {
            writer.uint32(8).uint32(message.programIdIndex);
        }
        if (message.accounts.length !== 0) {
            writer.uint32(18).bytes(message.accounts);
        }
        if (message.data.length !== 0) {
            writer.uint32(26).bytes(message.data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCompiledInstruction();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.programIdIndex = reader.uint32();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.accounts = reader.bytes();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.data = reader.bytes();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
            accounts: isSet(object.accounts) ? bytesFromBase64(object.accounts) : new Uint8Array(0),
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.programIdIndex !== 0) {
            obj.programIdIndex = Math.round(message.programIdIndex);
        }
        if (message.accounts.length !== 0) {
            obj.accounts = base64FromBytes(message.accounts);
        }
        if (message.data.length !== 0) {
            obj.data = base64FromBytes(message.data);
        }
        return obj;
    },
    create: function (base) {
        return exports.CompiledInstruction.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseCompiledInstruction();
        message.programIdIndex = (_a = object.programIdIndex) !== null && _a !== void 0 ? _a : 0;
        message.accounts = (_b = object.accounts) !== null && _b !== void 0 ? _b : new Uint8Array(0);
        message.data = (_c = object.data) !== null && _c !== void 0 ? _c : new Uint8Array(0);
        return message;
    },
};
function createBaseTokenBalance() {
    return { accountIndex: 0, mint: "", uiTokenAmount: undefined, owner: "", programId: "" };
}
exports.TokenBalance = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.accountIndex !== 0) {
            writer.uint32(8).uint32(message.accountIndex);
        }
        if (message.mint !== "") {
            writer.uint32(18).string(message.mint);
        }
        if (message.uiTokenAmount !== undefined) {
            exports.UiTokenAmount.encode(message.uiTokenAmount, writer.uint32(26).fork()).ldelim();
        }
        if (message.owner !== "") {
            writer.uint32(34).string(message.owner);
        }
        if (message.programId !== "") {
            writer.uint32(42).string(message.programId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTokenBalance();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.accountIndex = reader.uint32();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.mint = reader.string();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.uiTokenAmount = exports.UiTokenAmount.decode(reader, reader.uint32());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.owner = reader.string();
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.programId = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accountIndex: isSet(object.accountIndex) ? globalThis.Number(object.accountIndex) : 0,
            mint: isSet(object.mint) ? globalThis.String(object.mint) : "",
            uiTokenAmount: isSet(object.uiTokenAmount) ? exports.UiTokenAmount.fromJSON(object.uiTokenAmount) : undefined,
            owner: isSet(object.owner) ? globalThis.String(object.owner) : "",
            programId: isSet(object.programId) ? globalThis.String(object.programId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accountIndex !== 0) {
            obj.accountIndex = Math.round(message.accountIndex);
        }
        if (message.mint !== "") {
            obj.mint = message.mint;
        }
        if (message.uiTokenAmount !== undefined) {
            obj.uiTokenAmount = exports.UiTokenAmount.toJSON(message.uiTokenAmount);
        }
        if (message.owner !== "") {
            obj.owner = message.owner;
        }
        if (message.programId !== "") {
            obj.programId = message.programId;
        }
        return obj;
    },
    create: function (base) {
        return exports.TokenBalance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseTokenBalance();
        message.accountIndex = (_a = object.accountIndex) !== null && _a !== void 0 ? _a : 0;
        message.mint = (_b = object.mint) !== null && _b !== void 0 ? _b : "";
        message.uiTokenAmount = (object.uiTokenAmount !== undefined && object.uiTokenAmount !== null)
            ? exports.UiTokenAmount.fromPartial(object.uiTokenAmount)
            : undefined;
        message.owner = (_c = object.owner) !== null && _c !== void 0 ? _c : "";
        message.programId = (_d = object.programId) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseUiTokenAmount() {
    return { uiAmount: 0, decimals: 0, amount: "", uiAmountString: "" };
}
exports.UiTokenAmount = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.uiAmount !== 0) {
            writer.uint32(9).double(message.uiAmount);
        }
        if (message.decimals !== 0) {
            writer.uint32(16).uint32(message.decimals);
        }
        if (message.amount !== "") {
            writer.uint32(26).string(message.amount);
        }
        if (message.uiAmountString !== "") {
            writer.uint32(34).string(message.uiAmountString);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseUiTokenAmount();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 9) {
                        break;
                    }
                    message.uiAmount = reader.double();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.decimals = reader.uint32();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.amount = reader.string();
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.uiAmountString = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            uiAmount: isSet(object.uiAmount) ? globalThis.Number(object.uiAmount) : 0,
            decimals: isSet(object.decimals) ? globalThis.Number(object.decimals) : 0,
            amount: isSet(object.amount) ? globalThis.String(object.amount) : "",
            uiAmountString: isSet(object.uiAmountString) ? globalThis.String(object.uiAmountString) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.uiAmount !== 0) {
            obj.uiAmount = message.uiAmount;
        }
        if (message.decimals !== 0) {
            obj.decimals = Math.round(message.decimals);
        }
        if (message.amount !== "") {
            obj.amount = message.amount;
        }
        if (message.uiAmountString !== "") {
            obj.uiAmountString = message.uiAmountString;
        }
        return obj;
    },
    create: function (base) {
        return exports.UiTokenAmount.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseUiTokenAmount();
        message.uiAmount = (_a = object.uiAmount) !== null && _a !== void 0 ? _a : 0;
        message.decimals = (_b = object.decimals) !== null && _b !== void 0 ? _b : 0;
        message.amount = (_c = object.amount) !== null && _c !== void 0 ? _c : "";
        message.uiAmountString = (_d = object.uiAmountString) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseReturnData() {
    return { programId: new Uint8Array(0), data: new Uint8Array(0) };
}
exports.ReturnData = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.programId.length !== 0) {
            writer.uint32(10).bytes(message.programId);
        }
        if (message.data.length !== 0) {
            writer.uint32(18).bytes(message.data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseReturnData();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.programId = reader.bytes();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.data = reader.bytes();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            programId: isSet(object.programId) ? bytesFromBase64(object.programId) : new Uint8Array(0),
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.programId.length !== 0) {
            obj.programId = base64FromBytes(message.programId);
        }
        if (message.data.length !== 0) {
            obj.data = base64FromBytes(message.data);
        }
        return obj;
    },
    create: function (base) {
        return exports.ReturnData.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseReturnData();
        message.programId = (_a = object.programId) !== null && _a !== void 0 ? _a : new Uint8Array(0);
        message.data = (_b = object.data) !== null && _b !== void 0 ? _b : new Uint8Array(0);
        return message;
    },
};
function createBaseReward() {
    return { pubkey: "", lamports: "0", postBalance: "0", rewardType: 0, commission: "" };
}
exports.Reward = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.pubkey !== "") {
            writer.uint32(10).string(message.pubkey);
        }
        if (message.lamports !== "0") {
            writer.uint32(16).int64(message.lamports);
        }
        if (message.postBalance !== "0") {
            writer.uint32(24).uint64(message.postBalance);
        }
        if (message.rewardType !== 0) {
            writer.uint32(32).int32(message.rewardType);
        }
        if (message.commission !== "") {
            writer.uint32(42).string(message.commission);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseReward();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.pubkey = reader.string();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.lamports = longToString(reader.int64());
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.postBalance = longToString(reader.uint64());
                    continue;
                case 4:
                    if (tag !== 32) {
                        break;
                    }
                    message.rewardType = reader.int32();
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.commission = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            pubkey: isSet(object.pubkey) ? globalThis.String(object.pubkey) : "",
            lamports: isSet(object.lamports) ? globalThis.String(object.lamports) : "0",
            postBalance: isSet(object.postBalance) ? globalThis.String(object.postBalance) : "0",
            rewardType: isSet(object.rewardType) ? rewardTypeFromJSON(object.rewardType) : 0,
            commission: isSet(object.commission) ? globalThis.String(object.commission) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.pubkey !== "") {
            obj.pubkey = message.pubkey;
        }
        if (message.lamports !== "0") {
            obj.lamports = message.lamports;
        }
        if (message.postBalance !== "0") {
            obj.postBalance = message.postBalance;
        }
        if (message.rewardType !== 0) {
            obj.rewardType = rewardTypeToJSON(message.rewardType);
        }
        if (message.commission !== "") {
            obj.commission = message.commission;
        }
        return obj;
    },
    create: function (base) {
        return exports.Reward.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseReward();
        message.pubkey = (_a = object.pubkey) !== null && _a !== void 0 ? _a : "";
        message.lamports = (_b = object.lamports) !== null && _b !== void 0 ? _b : "0";
        message.postBalance = (_c = object.postBalance) !== null && _c !== void 0 ? _c : "0";
        message.rewardType = (_d = object.rewardType) !== null && _d !== void 0 ? _d : 0;
        message.commission = (_e = object.commission) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseRewards() {
    return { rewards: [], numPartitions: undefined };
}
exports.Rewards = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        for (var _i = 0, _a = message.rewards; _i < _a.length; _i++) {
            var v = _a[_i];
            exports.Reward.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.numPartitions !== undefined) {
            exports.NumPartitions.encode(message.numPartitions, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRewards();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.rewards.push(exports.Reward.decode(reader, reader.uint32()));
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.numPartitions = exports.NumPartitions.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            rewards: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.rewards) ? object.rewards.map(function (e) { return exports.Reward.fromJSON(e); }) : [],
            numPartitions: isSet(object.numPartitions) ? exports.NumPartitions.fromJSON(object.numPartitions) : undefined,
        };
    },
    toJSON: function (message) {
        var _a;
        var obj = {};
        if ((_a = message.rewards) === null || _a === void 0 ? void 0 : _a.length) {
            obj.rewards = message.rewards.map(function (e) { return exports.Reward.toJSON(e); });
        }
        if (message.numPartitions !== undefined) {
            obj.numPartitions = exports.NumPartitions.toJSON(message.numPartitions);
        }
        return obj;
    },
    create: function (base) {
        return exports.Rewards.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseRewards();
        message.rewards = ((_a = object.rewards) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.Reward.fromPartial(e); })) || [];
        message.numPartitions = (object.numPartitions !== undefined && object.numPartitions !== null)
            ? exports.NumPartitions.fromPartial(object.numPartitions)
            : undefined;
        return message;
    },
};
function createBaseUnixTimestamp() {
    return { timestamp: "0" };
}
exports.UnixTimestamp = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.timestamp !== "0") {
            writer.uint32(8).int64(message.timestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseUnixTimestamp();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.timestamp = longToString(reader.int64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return { timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.timestamp !== "0") {
            obj.timestamp = message.timestamp;
        }
        return obj;
    },
    create: function (base) {
        return exports.UnixTimestamp.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseUnixTimestamp();
        message.timestamp = (_a = object.timestamp) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseBlockHeight() {
    return { blockHeight: "0" };
}
exports.BlockHeight = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.blockHeight !== "0") {
            writer.uint32(8).uint64(message.blockHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBlockHeight();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.blockHeight = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return { blockHeight: isSet(object.blockHeight) ? globalThis.String(object.blockHeight) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.blockHeight !== "0") {
            obj.blockHeight = message.blockHeight;
        }
        return obj;
    },
    create: function (base) {
        return exports.BlockHeight.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseBlockHeight();
        message.blockHeight = (_a = object.blockHeight) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseNumPartitions() {
    return { numPartitions: "0" };
}
exports.NumPartitions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_1.default.Writer.create(); }
        if (message.numPartitions !== "0") {
            writer.uint32(8).uint64(message.numPartitions);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_1.default.Reader ? input : minimal_1.default.Reader.create(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseNumPartitions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.numPartitions = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON: function (object) {
        return { numPartitions: isSet(object.numPartitions) ? globalThis.String(object.numPartitions) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.numPartitions !== "0") {
            obj.numPartitions = message.numPartitions;
        }
        return obj;
    },
    create: function (base) {
        return exports.NumPartitions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseNumPartitions();
        message.numPartitions = (_a = object.numPartitions) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function bytesFromBase64(b64) {
    if (globalThis.Buffer) {
        return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = globalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (globalThis.Buffer) {
        return globalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(globalThis.String.fromCharCode(byte));
        });
        return globalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_1.default.util.Long !== long_1.default) {
    minimal_1.default.util.Long = long_1.default;
    minimal_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
//# sourceMappingURL=solana-storage.js.map