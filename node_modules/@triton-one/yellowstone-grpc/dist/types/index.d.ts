/**
 * TypeScript/JavaScript client for gRPC Geyser.
 */
import { ChannelOptions } from "@grpc/grpc-js";
import { CommitmentLevel, GetLatestBlockhashResponse, GeyserClient, IsBlockhashValidResponse, SubscribeRequestAccountsDataSlice, SubscribeRequestFilterAccounts, SubscribeRequestFilterBlocks, SubscribeRequestFilterBlocksMeta, SubscribeRequestFilterEntry, SubscribeRequestFilterSlots, SubscribeRequestFilterTransactions, SubscribeUpdateTransactionInfo } from "./grpc/geyser";
export { CommitmentLevel, SubscribeRequest, SubscribeRequestAccountsDataSlice, SubscribeRequestFilterAccounts, SubscribeRequestFilterAccountsFilter, SubscribeRequestFilterAccountsFilterMemcmp, SubscribeRequestFilterAccountsFilterLamports, SubscribeRequestFilterBlocks, SubscribeRequestFilterBlocksMeta, SubscribeRequestFilterEntry, SubscribeRequestFilterSlots, SubscribeRequestFilterTransactions, SubscribeRequest_AccountsEntry, SubscribeRequest_BlocksEntry, SubscribeRequest_BlocksMetaEntry, SubscribeRequest_SlotsEntry, SubscribeRequest_TransactionsEntry, SubscribeUpdate, SubscribeUpdateAccount, SubscribeUpdateAccountInfo, SubscribeUpdateBlock, SubscribeUpdateBlockMeta, SubscribeUpdatePing, SubscribeUpdateSlot, SubscribeUpdateTransaction, SubscribeUpdateTransactionInfo, } from "./grpc/geyser";
import * as wasm from "./encoding/yellowstone_grpc_solana_encoding_wasm";
import type { TransactionErrorSolana, MapTransactionEncodingToReturnType } from "./types";
export declare const txEncode: {
    encoding: typeof wasm.WasmUiTransactionEncoding;
    encode_raw: typeof wasm.encode_tx;
    encode: <T extends wasm.WasmUiTransactionEncoding>(message: SubscribeUpdateTransactionInfo, encoding: T, max_supported_transaction_version: number | undefined, show_rewards: boolean) => MapTransactionEncodingToReturnType[T];
};
export declare const txErrDecode: {
    decode_raw: typeof wasm.decode_tx_error;
    decode: (buf: Uint8Array) => TransactionErrorSolana;
};
export default class Client {
    _client: GeyserClient;
    _insecureXToken: string | undefined;
    constructor(endpoint: string, xToken: string | undefined, channelOptions: ChannelOptions | undefined);
    private _getInsecureMetadata;
    subscribe(): Promise<import("@grpc/grpc-js").ClientDuplexStream<import("./grpc/geyser").SubscribeRequest, import("./grpc/geyser").SubscribeUpdate>>;
    subscribeOnce(accounts: {
        [key: string]: SubscribeRequestFilterAccounts;
    }, slots: {
        [key: string]: SubscribeRequestFilterSlots;
    }, transactions: {
        [key: string]: SubscribeRequestFilterTransactions;
    }, transactionsStatus: {
        [key: string]: SubscribeRequestFilterTransactions;
    }, entry: {
        [key: string]: SubscribeRequestFilterEntry;
    }, blocks: {
        [key: string]: SubscribeRequestFilterBlocks;
    }, blocksMeta: {
        [key: string]: SubscribeRequestFilterBlocksMeta;
    }, commitment: CommitmentLevel | undefined, accountsDataSlice: SubscribeRequestAccountsDataSlice[]): Promise<import("@grpc/grpc-js").ClientDuplexStream<import("./grpc/geyser").SubscribeRequest, import("./grpc/geyser").SubscribeUpdate>>;
    ping(count: number): Promise<number>;
    getLatestBlockhash(commitment?: CommitmentLevel): Promise<GetLatestBlockhashResponse>;
    getBlockHeight(commitment?: CommitmentLevel): Promise<string>;
    getSlot(commitment?: CommitmentLevel): Promise<string>;
    isBlockhashValid(blockhash: string, commitment?: CommitmentLevel): Promise<IsBlockhashValidResponse>;
    getVersion(): Promise<string>;
}
