/* tslint:disable */
/* eslint-disable */
export function encode_tx(data: Uint8Array, encoding: WasmUiTransactionEncoding, max_supported_transaction_version: number | null | undefined, show_rewards: boolean): string;
export function decode_tx_error(err: Uint8Array): string;
/**
 * Initialize Javascript logging and panic handler
 */
export function solana_program_init(): void;
export enum WasmUiTransactionEncoding {
  Binary = 0,
  Base64 = 1,
  Base58 = 2,
  Json = 3,
  JsonParsed = 4,
}
/**
 * Authenticated encryption nonce and ciphertext
 */
export class AeCiphertext {
  private constructor();
  free(): void;
}
export class AeKey {
  private constructor();
  free(): void;
  /**
   * Generates a random authenticated encryption key.
   *
   * This function is randomized. It internally samples a scalar element using `OsRng`.
   */
  static newRand(): AeKey;
  /**
   * Encrypts an amount under the authenticated encryption key.
   */
  encrypt(amount: bigint): AeCiphertext;
  decrypt(ciphertext: AeCiphertext): bigint | undefined;
}
/**
 * Batched grouped ciphertext validity proof with two handles.
 *
 * A batched grouped ciphertext validity proof certifies the validity of two instances of a
 * standard ciphertext validity proof. An instance of a standard validity proof consists of one
 * ciphertext and two decryption handles: `(commitment, first_handle, second_handle)`. An
 * instance of a batched ciphertext validity proof is a pair `(commitment_0,
 * first_handle_0, second_handle_0)` and `(commitment_1, first_handle_1,
 * second_handle_1)`. The proof certifies the analogous decryptable properties for each one of
 * these pairs of commitment and decryption handles.
 */
export class BatchedGroupedCiphertext2HandlesValidityProof {
  private constructor();
  free(): void;
}
export class BatchedGroupedCiphertext2HandlesValidityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): BatchedGroupedCiphertext2HandlesValidityProofContext;
  first_pubkey: PodElGamalPubkey;
  second_pubkey: PodElGamalPubkey;
  grouped_ciphertext_lo: PodGroupedElGamalCiphertext2Handles;
  grouped_ciphertext_hi: PodGroupedElGamalCiphertext2Handles;
}
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyBatchedGroupedCiphertextValidity` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class BatchedGroupedCiphertext2HandlesValidityProofData {
  private constructor();
  free(): void;
  static new(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, grouped_ciphertext_lo: GroupedElGamalCiphertext2Handles, grouped_ciphertext_hi: GroupedElGamalCiphertext2Handles, amount_lo: bigint, amount_hi: bigint, opening_lo: PedersenOpening, opening_hi: PedersenOpening): BatchedGroupedCiphertext2HandlesValidityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): BatchedGroupedCiphertext2HandlesValidityProofData;
  context: BatchedGroupedCiphertext2HandlesValidityProofContext;
  proof: PodBatchedGroupedCiphertext2HandlesValidityProof;
}
/**
 * Batched grouped ciphertext validity proof with two handles.
 */
export class BatchedGroupedCiphertext3HandlesValidityProof {
  private constructor();
  free(): void;
}
export class BatchedGroupedCiphertext3HandlesValidityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): BatchedGroupedCiphertext3HandlesValidityProofContext;
  first_pubkey: PodElGamalPubkey;
  second_pubkey: PodElGamalPubkey;
  third_pubkey: PodElGamalPubkey;
  grouped_ciphertext_lo: PodGroupedElGamalCiphertext3Handles;
  grouped_ciphertext_hi: PodGroupedElGamalCiphertext3Handles;
}
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyBatchedGroupedCiphertext3HandlesValidity` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class BatchedGroupedCiphertext3HandlesValidityProofData {
  private constructor();
  free(): void;
  static new(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, third_pubkey: ElGamalPubkey, grouped_ciphertext_lo: GroupedElGamalCiphertext3Handles, grouped_ciphertext_hi: GroupedElGamalCiphertext3Handles, amount_lo: bigint, amount_hi: bigint, opening_lo: PedersenOpening, opening_hi: PedersenOpening): BatchedGroupedCiphertext3HandlesValidityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): BatchedGroupedCiphertext3HandlesValidityProofData;
  context: BatchedGroupedCiphertext3HandlesValidityProofContext;
  proof: PodBatchedGroupedCiphertext3HandlesValidityProof;
}
/**
 * The ciphertext-ciphertext equality proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
export class CiphertextCiphertextEqualityProof {
  private constructor();
  free(): void;
}
/**
 * The context data needed to verify a ciphertext-ciphertext equality proof.
 */
export class CiphertextCiphertextEqualityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): CiphertextCiphertextEqualityProofContext;
  first_pubkey: PodElGamalPubkey;
  second_pubkey: PodElGamalPubkey;
  first_ciphertext: PodElGamalCiphertext;
  second_ciphertext: PodElGamalCiphertext;
}
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyCiphertextCiphertextEquality` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class CiphertextCiphertextEqualityProofData {
  private constructor();
  free(): void;
  static new(first_keypair: ElGamalKeypair, second_pubkey: ElGamalPubkey, first_ciphertext: ElGamalCiphertext, second_ciphertext: ElGamalCiphertext, second_opening: PedersenOpening, amount: bigint): CiphertextCiphertextEqualityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): CiphertextCiphertextEqualityProofData;
  context: CiphertextCiphertextEqualityProofContext;
  proof: PodCiphertextCiphertextEqualityProof;
}
/**
 * Equality proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
export class CiphertextCommitmentEqualityProof {
  private constructor();
  free(): void;
}
/**
 * The context data needed to verify a ciphertext-commitment equality proof.
 */
export class CiphertextCommitmentEqualityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): CiphertextCommitmentEqualityProofContext;
  /**
   * The ElGamal pubkey
   */
  pubkey: PodElGamalPubkey;
  /**
   * The ciphertext encrypted under the ElGamal pubkey
   */
  ciphertext: PodElGamalCiphertext;
  /**
   * The Pedersen commitment
   */
  commitment: PodPedersenCommitment;
}
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyCiphertextCommitmentEquality` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class CiphertextCommitmentEqualityProofData {
  private constructor();
  free(): void;
  static new(keypair: ElGamalKeypair, ciphertext: ElGamalCiphertext, commitment: PedersenCommitment, opening: PedersenOpening, amount: bigint): CiphertextCommitmentEqualityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): CiphertextCommitmentEqualityProofData;
  context: CiphertextCommitmentEqualityProofContext;
  proof: PodCiphertextCommitmentEqualityProof;
}
/**
 * Decryption handle for Pedersen commitment.
 */
export class DecryptHandle {
  private constructor();
  free(): void;
}
/**
 * Ciphertext for the ElGamal encryption scheme.
 */
export class ElGamalCiphertext {
  private constructor();
  free(): void;
  commitment: PedersenCommitment;
  handle: DecryptHandle;
}
/**
 * A (twisted) ElGamal encryption keypair.
 *
 * The instances of the secret key are zeroized on drop.
 */
export class ElGamalKeypair {
  private constructor();
  free(): void;
  /**
   * Generates the public and secret keys for ElGamal encryption.
   *
   * This function is randomized. It internally samples a scalar element using `OsRng`.
   */
  static newRand(): ElGamalKeypair;
  pubkeyOwned(): ElGamalPubkey;
}
/**
 * Public key for the ElGamal encryption scheme.
 */
export class ElGamalPubkey {
  private constructor();
  free(): void;
  encryptU64(amount: bigint): ElGamalCiphertext;
  encryptWithU64(amount: bigint, opening: PedersenOpening): ElGamalCiphertext;
}
/**
 * The grouped ciphertext validity proof for 2 handles.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
export class GroupedCiphertext2HandlesValidityProof {
  private constructor();
  free(): void;
}
export class GroupedCiphertext2HandlesValidityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): GroupedCiphertext2HandlesValidityProofContext;
  first_pubkey: PodElGamalPubkey;
  second_pubkey: PodElGamalPubkey;
  grouped_ciphertext: PodGroupedElGamalCiphertext2Handles;
}
/**
 * The instruction data that is needed for the `ProofInstruction::VerifyGroupedCiphertextValidity`
 * instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class GroupedCiphertext2HandlesValidityProofData {
  private constructor();
  free(): void;
  static new(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, grouped_ciphertext: GroupedElGamalCiphertext2Handles, amount: bigint, opening: PedersenOpening): GroupedCiphertext2HandlesValidityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): GroupedCiphertext2HandlesValidityProofData;
  context: GroupedCiphertext2HandlesValidityProofContext;
  proof: PodGroupedCiphertext2HandlesValidityProof;
}
/**
 * The grouped ciphertext validity proof for 3 handles.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
export class GroupedCiphertext3HandlesValidityProof {
  private constructor();
  free(): void;
}
export class GroupedCiphertext3HandlesValidityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): GroupedCiphertext3HandlesValidityProofContext;
  first_pubkey: PodElGamalPubkey;
  second_pubkey: PodElGamalPubkey;
  third_pubkey: PodElGamalPubkey;
  grouped_ciphertext: PodGroupedElGamalCiphertext3Handles;
}
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyGroupedCiphertext3HandlesValidity` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class GroupedCiphertext3HandlesValidityProofData {
  private constructor();
  free(): void;
  static new(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, third_pubkey: ElGamalPubkey, grouped_ciphertext: GroupedElGamalCiphertext3Handles, amount: bigint, opening: PedersenOpening): GroupedCiphertext3HandlesValidityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): GroupedCiphertext3HandlesValidityProofData;
  context: GroupedCiphertext3HandlesValidityProofContext;
  proof: PodGroupedCiphertext3HandlesValidityProof;
}
export class GroupedElGamalCiphertext2Handles {
  private constructor();
  free(): void;
  static encryptU64(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, amount: bigint): GroupedElGamalCiphertext2Handles;
  static encryptionWithU64(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, amount: bigint, opening: PedersenOpening): GroupedElGamalCiphertext2Handles;
}
export class GroupedElGamalCiphertext3Handles {
  private constructor();
  free(): void;
  static encryptU64(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, third_pubkey: ElGamalPubkey, amount: bigint): GroupedElGamalCiphertext3Handles;
  static encryptionWithU64(first_pubkey: ElGamalPubkey, second_pubkey: ElGamalPubkey, third_pubkey: ElGamalPubkey, amount: bigint, opening: PedersenOpening): GroupedElGamalCiphertext3Handles;
}
/**
 * A hash; the 32-byte output of a hashing algorithm.
 *
 * This struct is used most often in `solana-sdk` and related crates to contain
 * a [SHA-256] hash, but may instead contain a [blake3] hash.
 *
 * [SHA-256]: https://en.wikipedia.org/wiki/SHA-2
 * [blake3]: https://github.com/BLAKE3-team/BLAKE3
 */
export class Hash {
  free(): void;
  /**
   * Create a new Hash object
   *
   * * `value` - optional hash as a base58 encoded string, `Uint8Array`, `[number]`
   */
  constructor(value: any);
  /**
   * Return the base58 string representation of the hash
   */
  toString(): string;
  /**
   * Checks if two `Hash`s are equal
   */
  equals(other: Hash): boolean;
  /**
   * Return the `Uint8Array` representation of the hash
   */
  toBytes(): Uint8Array;
}
/**
 * wasm-bindgen version of the Instruction struct.
 * This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
 * is fixed. This must not diverge from the regular non-wasm Instruction struct.
 */
export class Instruction {
  private constructor();
  free(): void;
}
export class Instructions {
  free(): void;
  constructor();
  push(instruction: Instruction): void;
}
/**
 * A vanilla Ed25519 key pair
 */
export class Keypair {
  free(): void;
  /**
   * Create a new `Keypair `
   */
  constructor();
  /**
   * Convert a `Keypair` to a `Uint8Array`
   */
  toBytes(): Uint8Array;
  /**
   * Recover a `Keypair` from a `Uint8Array`
   */
  static fromBytes(bytes: Uint8Array): Keypair;
  /**
   * Return the `Pubkey` for this `Keypair`
   */
  pubkey(): Pubkey;
}
/**
 * wasm-bindgen version of the Message struct.
 * This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
 * is fixed. This must not diverge from the regular non-wasm Message struct.
 */
export class Message {
  private constructor();
  free(): void;
  /**
   * The id of a recent ledger entry.
   */
  recent_blockhash: Hash;
}
/**
 * Algorithm handle for the Pedersen commitment scheme.
 */
export class Pedersen {
  private constructor();
  free(): void;
  static withU64(amount: bigint, opening: PedersenOpening): PedersenCommitment;
}
/**
 * Pedersen commitment type.
 */
export class PedersenCommitment {
  private constructor();
  free(): void;
}
/**
 * Pedersen opening type.
 *
 * Instances of Pedersen openings are zeroized on drop.
 */
export class PedersenOpening {
  private constructor();
  free(): void;
  static newRand(): PedersenOpening;
}
/**
 * Percentage-with-cap proof.
 *
 * The proof consists of two main components: `percentage_max_proof` and
 * `percentage_equality_proof`. If the committed amount is greater than the maximum cap value,
 * then the `percentage_max_proof` is properly generated and `percentage_equality_proof` is
 * simulated. If the encrypted amount is smaller than the maximum cap bound, the
 * `percentage_equality_proof` is properly generated and `percentage_max_proof` is simulated.
 */
export class PercentageWithCapProof {
  private constructor();
  free(): void;
}
/**
 * The context data needed to verify a percentage-with-cap proof.
 *
 * We refer to [`ZK ElGamal proof`] for the formal details on how the percentage-with-cap proof is
 * computed.
 *
 * [`ZK ElGamal proof`]: https://docs.solanalabs.com/runtime/zk-token-proof
 */
export class PercentageWithCapProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): PercentageWithCapProofContext;
  /**
   * The Pedersen commitment to the percentage amount.
   */
  percentage_commitment: PodPedersenCommitment;
  /**
   * The Pedersen commitment to the delta amount.
   */
  delta_commitment: PodPedersenCommitment;
  /**
   * The Pedersen commitment to the claimed amount.
   */
  claimed_commitment: PodPedersenCommitment;
  /**
   * The maximum cap bound.
   */
  max_value: PodU64;
}
/**
 * The instruction data that is needed for the `ProofInstruction::VerifyPercentageWithCap`
 * instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class PercentageWithCapProofData {
  private constructor();
  free(): void;
  static new(percentage_commitment: PedersenCommitment, percentage_opening: PedersenOpening, percentage_amount: bigint, delta_commitment: PedersenCommitment, delta_opening: PedersenOpening, delta_amount: bigint, claimed_commitment: PedersenCommitment, claimed_opening: PedersenOpening, max_value: bigint): PercentageWithCapProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): PercentageWithCapProofData;
  context: PercentageWithCapProofContext;
  proof: PodPercentageWithCapProof;
}
/**
 * The `AeCiphertext` type as a `Pod`.
 */
export class PodAeCiphertext {
  free(): void;
  constructor(value: any);
  toString(): string;
  equals(other: PodAeCiphertext): boolean;
  toBytes(): Uint8Array;
  static zeroed(): PodAeCiphertext;
  static encode(decoded: AeCiphertext): PodAeCiphertext;
  decode(): AeCiphertext;
}
/**
 * The `BatchedGroupedCiphertext2HandlesValidityProof` type as a `Pod`.
 */
export class PodBatchedGroupedCiphertext2HandlesValidityProof {
  private constructor();
  free(): void;
}
/**
 * The `BatchedGroupedCiphertext3HandlesValidityProof` type as a `Pod`.
 */
export class PodBatchedGroupedCiphertext3HandlesValidityProof {
  private constructor();
  free(): void;
}
/**
 * The `CiphertextCiphertextEqualityProof` type as a `Pod`.
 */
export class PodCiphertextCiphertextEqualityProof {
  private constructor();
  free(): void;
}
/**
 * The `CiphertextCommitmentEqualityProof` type as a `Pod`.
 */
export class PodCiphertextCommitmentEqualityProof {
  private constructor();
  free(): void;
}
/**
 * The `ElGamalCiphertext` type as a `Pod`.
 */
export class PodElGamalCiphertext {
  free(): void;
  constructor(value: any);
  toString(): string;
  equals(other: PodElGamalCiphertext): boolean;
  toBytes(): Uint8Array;
  static zeroed(): PodElGamalCiphertext;
  static encode(decoded: ElGamalCiphertext): PodElGamalCiphertext;
  decode(): ElGamalCiphertext;
}
/**
 * The `ElGamalPubkey` type as a `Pod`.
 */
export class PodElGamalPubkey {
  free(): void;
  constructor(value: any);
  toString(): string;
  equals(other: PodElGamalPubkey): boolean;
  toBytes(): Uint8Array;
  static zeroed(): PodElGamalPubkey;
  static encode(decoded: ElGamalPubkey): PodElGamalPubkey;
  decode(): ElGamalPubkey;
}
/**
 * The `GroupedCiphertext2HandlesValidityProof` type as a `Pod`.
 */
export class PodGroupedCiphertext2HandlesValidityProof {
  private constructor();
  free(): void;
}
/**
 * The `GroupedCiphertext3HandlesValidityProof` type as a `Pod`.
 */
export class PodGroupedCiphertext3HandlesValidityProof {
  private constructor();
  free(): void;
}
/**
 * The `GroupedElGamalCiphertext` type with two decryption handles as a `Pod`
 */
export class PodGroupedElGamalCiphertext2Handles {
  private constructor();
  free(): void;
}
/**
 * The `GroupedElGamalCiphertext` type with three decryption handles as a `Pod`
 */
export class PodGroupedElGamalCiphertext3Handles {
  private constructor();
  free(): void;
}
/**
 * The `PedersenCommitment` type as a `Pod`.
 */
export class PodPedersenCommitment {
  private constructor();
  free(): void;
}
/**
 * The `PercentageWithCapProof` type as a `Pod`.
 */
export class PodPercentageWithCapProof {
  private constructor();
  free(): void;
}
/**
 * The `PubkeyValidityProof` type as a `Pod`.
 */
export class PodPubkeyValidityProof {
  private constructor();
  free(): void;
}
export class PodU64 {
  private constructor();
  free(): void;
}
/**
 * The `ZeroCiphertextProof` type as a `Pod`.
 */
export class PodZeroCiphertextProof {
  private constructor();
  free(): void;
}
/**
 * The address of a [Solana account][acc].
 *
 * Some account addresses are [ed25519] public keys, with corresponding secret
 * keys that are managed off-chain. Often, though, account addresses do not
 * have corresponding secret keys &mdash; as with [_program derived
 * addresses_][pdas] &mdash; or the secret key is not relevant to the operation
 * of a program, and may have even been disposed of. As running Solana programs
 * can not safely create or manage secret keys, the full [`Keypair`] is not
 * defined in `solana-program` but in `solana-sdk`.
 *
 * [acc]: https://solana.com/docs/core/accounts
 * [ed25519]: https://ed25519.cr.yp.to/
 * [pdas]: https://solana.com/docs/core/cpi#program-derived-addresses
 * [`Keypair`]: https://docs.rs/solana-sdk/latest/solana_sdk/signer/keypair/struct.Keypair.html
 */
export class Pubkey {
  free(): void;
  /**
   * Create a new Pubkey object
   *
   * * `value` - optional public key as a base58 encoded string, `Uint8Array`, `[number]`
   */
  constructor(value: any);
  /**
   * Return the base58 string representation of the public key
   */
  toString(): string;
  /**
   * Check if a `Pubkey` is on the ed25519 curve.
   */
  isOnCurve(): boolean;
  /**
   * Checks if two `Pubkey`s are equal
   */
  equals(other: Pubkey): boolean;
  /**
   * Return the `Uint8Array` representation of the public key
   */
  toBytes(): Uint8Array;
  /**
   * Derive a Pubkey from another Pubkey, string seed, and a program id
   */
  static createWithSeed(base: Pubkey, seed: string, owner: Pubkey): Pubkey;
  /**
   * Derive a program address from seeds and a program id
   */
  static createProgramAddress(seeds: any[], program_id: Pubkey): Pubkey;
  /**
   * Find a valid program address
   *
   * Returns:
   * * `[PubKey, number]` - the program address and bump seed
   */
  static findProgramAddress(seeds: any[], program_id: Pubkey): any;
}
/**
 * Public-key proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
export class PubkeyValidityProof {
  private constructor();
  free(): void;
}
/**
 * The context data needed to verify a pubkey validity proof.
 */
export class PubkeyValidityProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): PubkeyValidityProofContext;
  /**
   * The public key to be proved
   */
  pubkey: PodElGamalPubkey;
}
/**
 * The instruction data that is needed for the `ProofInstruction::VerifyPubkeyValidity`
 * instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class PubkeyValidityProofData {
  private constructor();
  free(): void;
  static new(keypair: ElGamalKeypair): PubkeyValidityProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): PubkeyValidityProofData;
  /**
   * The context data for the public key validity proof
   */
  context: PubkeyValidityProofContext;
  /**
   * Proof that the public key is well-formed
   */
  proof: PodPubkeyValidityProof;
}
/**
 * wasm-bindgen version of the Transaction struct.
 * This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
 * is fixed. This must not diverge from the regular non-wasm Transaction struct.
 */
export class Transaction {
  free(): void;
  /**
   * Create a new `Transaction`
   */
  constructor(instructions: Instructions, payer?: Pubkey | null);
  /**
   * Return a message containing all data that should be signed.
   */
  message(): Message;
  /**
   * Return the serialized message data to sign.
   */
  messageData(): Uint8Array;
  /**
   * Verify the transaction
   */
  verify(): void;
  partialSign(keypair: Keypair, recent_blockhash: Hash): void;
  isSigned(): boolean;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): Transaction;
}
/**
 * Zero-ciphertext proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
export class ZeroCiphertextProof {
  private constructor();
  free(): void;
}
/**
 * The context data needed to verify a zero-ciphertext proof.
 */
export class ZeroCiphertextProofContext {
  private constructor();
  free(): void;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): ZeroCiphertextProofContext;
  /**
   * The ElGamal pubkey associated with the ElGamal ciphertext
   */
  pubkey: PodElGamalPubkey;
  /**
   * The ElGamal ciphertext that encrypts zero
   */
  ciphertext: PodElGamalCiphertext;
}
/**
 * The instruction data that is needed for the `ProofInstruction::ZeroCiphertext` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
export class ZeroCiphertextProofData {
  private constructor();
  free(): void;
  static new(keypair: ElGamalKeypair, ciphertext: ElGamalCiphertext): ZeroCiphertextProofData;
  toBytes(): Uint8Array;
  static fromBytes(bytes: Uint8Array): ZeroCiphertextProofData;
  /**
   * The context data for the zero-ciphertext proof
   */
  context: ZeroCiphertextProofContext;
  /**
   * Proof that the ciphertext is zero
   */
  proof: PodZeroCiphertextProof;
}
