"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var fakeGetTransition = function (signature, config) { return null; };
var signature = "";
var base58 = fakeGetTransition(signature, { encoding: "base58" });
var base64 = fakeGetTransition(signature, { encoding: "base64" });
var json = fakeGetTransition(signature, { encoding: "json" });
var jsonParsed = fakeGetTransition(signature, { encoding: "jsonParsed" });
//# sourceMappingURL=types.js.map