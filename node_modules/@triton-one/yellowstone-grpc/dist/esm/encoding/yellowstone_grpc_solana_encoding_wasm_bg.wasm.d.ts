/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const encode_tx: (a: number, b: number, c: number, d: number, e: number) => [number, number, number, number];
export const decode_tx_error: (a: number, b: number) => [number, number, number, number];
export const __wbg_hash_free: (a: number, b: number) => void;
export const hash_constructor: (a: any) => [number, number, number];
export const hash_toString: (a: number) => [number, number];
export const hash_equals: (a: number, b: number) => number;
export const hash_toBytes: (a: number) => [number, number];
export const __wbg_instructions_free: (a: number, b: number) => void;
export const instructions_constructor: () => number;
export const instructions_push: (a: number, b: number) => void;
export const __wbg_instruction_free: (a: number, b: number) => void;
export const __wbg_keypair_free: (a: number, b: number) => void;
export const keypair_constructor: () => number;
export const keypair_toBytes: (a: number) => [number, number];
export const keypair_fromBytes: (a: number, b: number) => [number, number, number];
export const keypair_pubkey: (a: number) => number;
export const __wbg_message_free: (a: number, b: number) => void;
export const __wbg_get_message_recent_blockhash: (a: number) => number;
export const __wbg_set_message_recent_blockhash: (a: number, b: number) => void;
export const pubkey_constructor: (a: any) => [number, number, number];
export const pubkey_toString: (a: number) => [number, number];
export const pubkey_isOnCurve: (a: number) => number;
export const pubkey_createWithSeed: (a: number, b: number, c: number, d: number) => [number, number, number];
export const pubkey_createProgramAddress: (a: number, b: number, c: number) => [number, number, number];
export const pubkey_findProgramAddress: (a: number, b: number, c: number) => [number, number, number];
export const systeminstruction_createAccount: (a: number, b: number, c: bigint, d: bigint, e: number) => number;
export const systeminstruction_createAccountWithSeed: (a: number, b: number, c: number, d: number, e: number, f: bigint, g: bigint, h: number) => number;
export const systeminstruction_assign: (a: number, b: number) => number;
export const systeminstruction_assignWithSeed: (a: number, b: number, c: number, d: number, e: number) => number;
export const systeminstruction_transfer: (a: number, b: number, c: bigint) => number;
export const systeminstruction_transferWithSeed: (a: number, b: number, c: number, d: number, e: number, f: number, g: bigint) => number;
export const systeminstruction_allocate: (a: number, b: bigint) => number;
export const systeminstruction_allocateWithSeed: (a: number, b: number, c: number, d: number, e: bigint, f: number) => number;
export const systeminstruction_createNonceAccount: (a: number, b: number, c: number, d: bigint) => any;
export const systeminstruction_advanceNonceAccount: (a: number, b: number) => number;
export const systeminstruction_withdrawNonceAccount: (a: number, b: number, c: number, d: bigint) => number;
export const systeminstruction_authorizeNonceAccount: (a: number, b: number, c: number) => number;
export const transaction_constructor: (a: number, b: number) => number;
export const transaction_message: (a: number) => number;
export const transaction_messageData: (a: number) => [number, number];
export const transaction_verify: (a: number) => [number, number];
export const transaction_partialSign: (a: number, b: number, c: number) => void;
export const transaction_isSigned: (a: number) => number;
export const transaction_toBytes: (a: number) => [number, number];
export const transaction_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_transaction_free: (a: number, b: number) => void;
export const __wbg_aekey_free: (a: number, b: number) => void;
export const aekey_newRand: () => number;
export const aekey_encrypt: (a: number, b: bigint) => number;
export const aekey_decrypt: (a: number, b: number) => [number, bigint];
export const __wbg_aeciphertext_free: (a: number, b: number) => void;
export const __wbg_elgamalkeypair_free: (a: number, b: number) => void;
export const elgamalkeypair_newRand: () => number;
export const elgamalkeypair_pubkeyOwned: (a: number) => number;
export const elgamalpubkey_encryptU64: (a: number, b: bigint) => number;
export const elgamalpubkey_encryptWithU64: (a: number, b: bigint, c: number) => number;
export const __wbg_get_elgamalciphertext_commitment: (a: number) => number;
export const __wbg_set_elgamalciphertext_commitment: (a: number, b: number) => void;
export const __wbg_get_elgamalciphertext_handle: (a: number) => number;
export const __wbg_set_elgamalciphertext_handle: (a: number, b: number) => void;
export const __wbg_groupedelgamalciphertext2handles_free: (a: number, b: number) => void;
export const groupedelgamalciphertext2handles_encryptU64: (a: number, b: number, c: bigint) => number;
export const groupedelgamalciphertext2handles_encryptionWithU64: (a: number, b: number, c: bigint, d: number) => number;
export const __wbg_groupedelgamalciphertext3handles_free: (a: number, b: number) => void;
export const groupedelgamalciphertext3handles_encryptU64: (a: number, b: number, c: number, d: bigint) => number;
export const groupedelgamalciphertext3handles_encryptionWithU64: (a: number, b: number, c: number, d: bigint, e: number) => number;
export const __wbg_pedersen_free: (a: number, b: number) => void;
export const pedersen_withU64: (a: bigint, b: number) => number;
export const __wbg_pedersenopening_free: (a: number, b: number) => void;
export const pedersenopening_newRand: () => number;
export const podaeciphertext_constructor: (a: any) => [number, number, number];
export const podaeciphertext_toString: (a: number) => [number, number];
export const podaeciphertext_equals: (a: number, b: number) => number;
export const podaeciphertext_toBytes: (a: number) => [number, number];
export const podaeciphertext_zeroed: () => number;
export const podaeciphertext_encode: (a: number) => number;
export const podaeciphertext_decode: (a: number) => [number, number, number];
export const __wbg_podelgamalciphertext_free: (a: number, b: number) => void;
export const podelgamalciphertext_constructor: (a: any) => [number, number, number];
export const podelgamalciphertext_toString: (a: number) => [number, number];
export const podelgamalciphertext_equals: (a: number, b: number) => number;
export const podelgamalciphertext_toBytes: (a: number) => [number, number];
export const podelgamalciphertext_encode: (a: number) => number;
export const podelgamalciphertext_decode: (a: number) => [number, number, number];
export const podelgamalpubkey_constructor: (a: any) => [number, number, number];
export const podelgamalpubkey_toString: (a: number) => [number, number];
export const podelgamalpubkey_zeroed: () => number;
export const podelgamalpubkey_encode: (a: number) => number;
export const podelgamalpubkey_decode: (a: number) => [number, number, number];
export const __wbg_podgroupedelgamalciphertext2handles_free: (a: number, b: number) => void;
export const __wbg_podu64_free: (a: number, b: number) => void;
export const __wbg_batchedgroupedciphertext2handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_batchedgroupedciphertext3handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_ciphertextciphertextequalityproof_free: (a: number, b: number) => void;
export const __wbg_batchedgroupedciphertext2handlesvalidityproofdata_free: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext2handlesvalidityproofdata_context: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext2handlesvalidityproofdata_context: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext2handlesvalidityproofdata_proof: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext2handlesvalidityproofdata_proof: (a: number, b: number) => void;
export const __wbg_batchedgroupedciphertext2handlesvalidityproofcontext_free: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_lo: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_lo: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_hi: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_hi: (a: number, b: number) => void;
export const batchedgroupedciphertext2handlesvalidityproofdata_new: (a: number, b: number, c: number, d: number, e: bigint, f: bigint, g: number, h: number) => [number, number, number];
export const batchedgroupedciphertext2handlesvalidityproofdata_toBytes: (a: number) => [number, number];
export const batchedgroupedciphertext2handlesvalidityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const batchedgroupedciphertext2handlesvalidityproofcontext_toBytes: (a: number) => [number, number];
export const batchedgroupedciphertext2handlesvalidityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_batchedgroupedciphertext3handlesvalidityproofdata_free: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofdata_context: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofdata_context: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofdata_proof: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofdata_proof: (a: number, b: number) => void;
export const __wbg_batchedgroupedciphertext3handlesvalidityproofcontext_free: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_lo: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_lo: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_hi: (a: number) => number;
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_hi: (a: number, b: number) => void;
export const batchedgroupedciphertext3handlesvalidityproofdata_new: (a: number, b: number, c: number, d: number, e: number, f: bigint, g: bigint, h: number, i: number) => [number, number, number];
export const batchedgroupedciphertext3handlesvalidityproofdata_toBytes: (a: number) => [number, number];
export const batchedgroupedciphertext3handlesvalidityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const batchedgroupedciphertext3handlesvalidityproofcontext_toBytes: (a: number) => [number, number];
export const batchedgroupedciphertext3handlesvalidityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_get_ciphertextciphertextequalityproofdata_context: (a: number) => number;
export const __wbg_set_ciphertextciphertextequalityproofdata_context: (a: number, b: number) => void;
export const __wbg_get_ciphertextciphertextequalityproofdata_proof: (a: number) => number;
export const __wbg_set_ciphertextciphertextequalityproofdata_proof: (a: number, b: number) => void;
export const __wbg_get_ciphertextciphertextequalityproofcontext_first_ciphertext: (a: number) => number;
export const __wbg_set_ciphertextciphertextequalityproofcontext_first_ciphertext: (a: number, b: number) => void;
export const __wbg_get_ciphertextciphertextequalityproofcontext_second_ciphertext: (a: number) => number;
export const __wbg_set_ciphertextciphertextequalityproofcontext_second_ciphertext: (a: number, b: number) => void;
export const ciphertextciphertextequalityproofdata_new: (a: number, b: number, c: number, d: number, e: number, f: bigint) => [number, number, number];
export const ciphertextciphertextequalityproofcontext_toBytes: (a: number) => [number, number];
export const ciphertextciphertextequalityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_ciphertextcommitmentequalityproofdata_free: (a: number, b: number) => void;
export const __wbg_get_ciphertextcommitmentequalityproofdata_context: (a: number) => number;
export const __wbg_set_ciphertextcommitmentequalityproofdata_context: (a: number, b: number) => void;
export const __wbg_get_ciphertextcommitmentequalityproofdata_proof: (a: number) => number;
export const __wbg_set_ciphertextcommitmentequalityproofdata_proof: (a: number, b: number) => void;
export const __wbg_ciphertextcommitmentequalityproofcontext_free: (a: number, b: number) => void;
export const __wbg_get_ciphertextcommitmentequalityproofcontext_ciphertext: (a: number) => number;
export const __wbg_set_ciphertextcommitmentequalityproofcontext_ciphertext: (a: number, b: number) => void;
export const __wbg_get_ciphertextcommitmentequalityproofcontext_commitment: (a: number) => number;
export const __wbg_set_ciphertextcommitmentequalityproofcontext_commitment: (a: number, b: number) => void;
export const ciphertextcommitmentequalityproofdata_new: (a: number, b: number, c: number, d: number, e: bigint) => [number, number, number];
export const ciphertextcommitmentequalityproofdata_toBytes: (a: number) => [number, number];
export const ciphertextcommitmentequalityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const ciphertextcommitmentequalityproofcontext_toBytes: (a: number) => [number, number];
export const ciphertextcommitmentequalityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_get_groupedciphertext2handlesvalidityproofdata_context: (a: number) => number;
export const __wbg_set_groupedciphertext2handlesvalidityproofdata_context: (a: number, b: number) => void;
export const __wbg_get_groupedciphertext2handlesvalidityproofdata_proof: (a: number) => number;
export const __wbg_set_groupedciphertext2handlesvalidityproofdata_proof: (a: number, b: number) => void;
export const groupedciphertext2handlesvalidityproofdata_new: (a: number, b: number, c: number, d: bigint, e: number) => [number, number, number];
export const groupedciphertext2handlesvalidityproofcontext_toBytes: (a: number) => [number, number];
export const groupedciphertext2handlesvalidityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_get_groupedciphertext3handlesvalidityproofdata_context: (a: number) => number;
export const __wbg_set_groupedciphertext3handlesvalidityproofdata_context: (a: number, b: number) => void;
export const __wbg_get_groupedciphertext3handlesvalidityproofdata_proof: (a: number) => number;
export const __wbg_set_groupedciphertext3handlesvalidityproofdata_proof: (a: number, b: number) => void;
export const groupedciphertext3handlesvalidityproofdata_new: (a: number, b: number, c: number, d: number, e: bigint, f: number) => [number, number, number];
export const groupedciphertext3handlesvalidityproofcontext_toBytes: (a: number) => [number, number];
export const groupedciphertext3handlesvalidityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_percentagewithcapproofdata_free: (a: number, b: number) => void;
export const __wbg_get_percentagewithcapproofdata_context: (a: number) => number;
export const __wbg_set_percentagewithcapproofdata_context: (a: number, b: number) => void;
export const __wbg_get_percentagewithcapproofdata_proof: (a: number) => number;
export const __wbg_set_percentagewithcapproofdata_proof: (a: number, b: number) => void;
export const __wbg_percentagewithcapproofcontext_free: (a: number, b: number) => void;
export const __wbg_get_percentagewithcapproofcontext_max_value: (a: number) => number;
export const __wbg_set_percentagewithcapproofcontext_max_value: (a: number, b: number) => void;
export const percentagewithcapproofdata_new: (a: number, b: number, c: bigint, d: number, e: number, f: bigint, g: number, h: number, i: bigint) => [number, number, number];
export const percentagewithcapproofdata_toBytes: (a: number) => [number, number];
export const percentagewithcapproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const percentagewithcapproofcontext_toBytes: (a: number) => [number, number];
export const percentagewithcapproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const pubkeyvalidityproofdata_new: (a: number) => [number, number, number];
export const pubkeyvalidityproofdata_toBytes: (a: number) => [number, number];
export const pubkeyvalidityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const pubkeyvalidityproofcontext_toBytes: (a: number) => [number, number];
export const pubkeyvalidityproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_get_zerociphertextproofdata_context: (a: number) => number;
export const __wbg_set_zerociphertextproofdata_context: (a: number, b: number) => void;
export const __wbg_get_zerociphertextproofdata_proof: (a: number) => number;
export const __wbg_set_zerociphertextproofdata_proof: (a: number, b: number) => void;
export const zerociphertextproofdata_new: (a: number, b: number) => [number, number, number];
export const rust_zstd_wasm_shim_qsort: (a: number, b: number, c: number, d: number) => void;
export const rust_zstd_wasm_shim_malloc: (a: number) => number;
export const rust_zstd_wasm_shim_memcmp: (a: number, b: number, c: number) => number;
export const rust_zstd_wasm_shim_calloc: (a: number, b: number) => number;
export const rust_zstd_wasm_shim_free: (a: number) => void;
export const rust_zstd_wasm_shim_memcpy: (a: number, b: number, c: number) => number;
export const rust_zstd_wasm_shim_memmove: (a: number, b: number, c: number) => number;
export const rust_zstd_wasm_shim_memset: (a: number, b: number, c: number) => number;
export const solana_program_init: () => void;
export const podelgamalciphertext_zeroed: () => number;
export const ciphertextciphertextequalityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const groupedciphertext2handlesvalidityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const groupedciphertext3handlesvalidityproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const zerociphertextproofdata_fromBytes: (a: number, b: number) => [number, number, number];
export const zerociphertextproofcontext_fromBytes: (a: number, b: number) => [number, number, number];
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_first_pubkey: (a: number, b: number) => void;
export const __wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_second_pubkey: (a: number, b: number) => void;
export const __wbg_set_ciphertextciphertextequalityproofcontext_first_pubkey: (a: number, b: number) => void;
export const __wbg_set_ciphertextciphertextequalityproofcontext_second_pubkey: (a: number, b: number) => void;
export const __wbg_set_ciphertextcommitmentequalityproofcontext_pubkey: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext2handlesvalidityproofcontext_first_pubkey: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext2handlesvalidityproofcontext_second_pubkey: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext2handlesvalidityproofcontext_grouped_ciphertext: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext3handlesvalidityproofcontext_first_pubkey: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext3handlesvalidityproofcontext_second_pubkey: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext3handlesvalidityproofcontext_third_pubkey: (a: number, b: number) => void;
export const __wbg_set_groupedciphertext3handlesvalidityproofcontext_grouped_ciphertext: (a: number, b: number) => void;
export const __wbg_set_percentagewithcapproofcontext_percentage_commitment: (a: number, b: number) => void;
export const __wbg_set_percentagewithcapproofcontext_delta_commitment: (a: number, b: number) => void;
export const __wbg_set_percentagewithcapproofcontext_claimed_commitment: (a: number, b: number) => void;
export const __wbg_set_pubkeyvalidityproofdata_context: (a: number, b: number) => void;
export const __wbg_set_pubkeyvalidityproofdata_proof: (a: number, b: number) => void;
export const __wbg_set_pubkeyvalidityproofcontext_pubkey: (a: number, b: number) => void;
export const __wbg_set_zerociphertextproofcontext_pubkey: (a: number, b: number) => void;
export const __wbg_set_zerociphertextproofcontext_ciphertext: (a: number, b: number) => void;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_first_pubkey: (a: number) => number;
export const __wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_second_pubkey: (a: number) => number;
export const __wbg_get_ciphertextciphertextequalityproofcontext_first_pubkey: (a: number) => number;
export const __wbg_get_ciphertextciphertextequalityproofcontext_second_pubkey: (a: number) => number;
export const __wbg_get_ciphertextcommitmentequalityproofcontext_pubkey: (a: number) => number;
export const __wbg_get_groupedciphertext2handlesvalidityproofcontext_first_pubkey: (a: number) => number;
export const __wbg_get_groupedciphertext2handlesvalidityproofcontext_second_pubkey: (a: number) => number;
export const __wbg_get_groupedciphertext2handlesvalidityproofcontext_grouped_ciphertext: (a: number) => number;
export const __wbg_get_groupedciphertext3handlesvalidityproofcontext_first_pubkey: (a: number) => number;
export const __wbg_get_groupedciphertext3handlesvalidityproofcontext_second_pubkey: (a: number) => number;
export const __wbg_get_groupedciphertext3handlesvalidityproofcontext_third_pubkey: (a: number) => number;
export const __wbg_get_groupedciphertext3handlesvalidityproofcontext_grouped_ciphertext: (a: number) => number;
export const __wbg_get_percentagewithcapproofcontext_percentage_commitment: (a: number) => number;
export const __wbg_get_percentagewithcapproofcontext_delta_commitment: (a: number) => number;
export const __wbg_get_percentagewithcapproofcontext_claimed_commitment: (a: number) => number;
export const __wbg_get_pubkeyvalidityproofdata_context: (a: number) => number;
export const __wbg_get_pubkeyvalidityproofdata_proof: (a: number) => number;
export const __wbg_get_pubkeyvalidityproofcontext_pubkey: (a: number) => number;
export const __wbg_get_zerociphertextproofcontext_pubkey: (a: number) => number;
export const __wbg_get_zerociphertextproofcontext_ciphertext: (a: number) => number;
export const __wbg_podciphertextcommitmentequalityproof_free: (a: number, b: number) => void;
export const __wbg_ciphertextciphertextequalityproofcontext_free: (a: number, b: number) => void;
export const __wbg_groupedciphertext2handlesvalidityproofcontext_free: (a: number, b: number) => void;
export const __wbg_elgamalpubkey_free: (a: number, b: number) => void;
export const __wbg_podgroupedelgamalciphertext3handles_free: (a: number, b: number) => void;
export const __wbg_decrypthandle_free: (a: number, b: number) => void;
export const __wbg_zerociphertextproofcontext_free: (a: number, b: number) => void;
export const __wbg_groupedciphertext2handlesvalidityproofdata_free: (a: number, b: number) => void;
export const __wbg_groupedciphertext2handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_podpercentagewithcapproof_free: (a: number, b: number) => void;
export const __wbg_pubkeyvalidityproof_free: (a: number, b: number) => void;
export const __wbg_podaeciphertext_free: (a: number, b: number) => void;
export const zerociphertextproofcontext_toBytes: (a: number) => [number, number];
export const __wbg_zerociphertextproof_free: (a: number, b: number) => void;
export const groupedciphertext2handlesvalidityproofdata_toBytes: (a: number) => [number, number];
export const __wbg_elgamalciphertext_free: (a: number, b: number) => void;
export const __wbg_percentagewithcapproof_free: (a: number, b: number) => void;
export const __wbg_pedersencommitment_free: (a: number, b: number) => void;
export const __wbg_groupedciphertext3handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_pubkeyvalidityproofdata_free: (a: number, b: number) => void;
export const __wbg_ciphertextcommitmentequalityproof_free: (a: number, b: number) => void;
export const __wbg_pubkeyvalidityproofcontext_free: (a: number, b: number) => void;
export const __wbg_podpubkeyvalidityproof_free: (a: number, b: number) => void;
export const zerociphertextproofdata_toBytes: (a: number) => [number, number];
export const __wbg_zerociphertextproofdata_free: (a: number, b: number) => void;
export const __wbg_groupedciphertext3handlesvalidityproofcontext_free: (a: number, b: number) => void;
export const __wbg_podpedersencommitment_free: (a: number, b: number) => void;
export const groupedciphertext3handlesvalidityproofdata_toBytes: (a: number) => [number, number];
export const __wbg_groupedciphertext3handlesvalidityproofdata_free: (a: number, b: number) => void;
export const __wbg_podgroupedciphertext2handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_podzerociphertextproof_free: (a: number, b: number) => void;
export const __wbg_podbatchedgroupedciphertext2handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_podgroupedciphertext3handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_podbatchedgroupedciphertext3handlesvalidityproof_free: (a: number, b: number) => void;
export const __wbg_podciphertextciphertextequalityproof_free: (a: number, b: number) => void;
export const ciphertextciphertextequalityproofdata_toBytes: (a: number) => [number, number];
export const __wbg_ciphertextciphertextequalityproofdata_free: (a: number, b: number) => void;
export const pubkey_toBytes: (a: number) => [number, number];
export const podelgamalpubkey_toBytes: (a: number) => [number, number];
export const pubkey_equals: (a: number, b: number) => number;
export const podelgamalpubkey_equals: (a: number, b: number) => number;
export const __wbg_pubkey_free: (a: number, b: number) => void;
export const __wbg_podelgamalpubkey_free: (a: number, b: number) => void;
export const __wbindgen_exn_store: (a: number) => void;
export const __externref_table_alloc: () => number;
export const __wbindgen_export_2: WebAssembly.Table;
export const __wbindgen_free: (a: number, b: number, c: number) => void;
export const __wbindgen_malloc: (a: number, b: number) => number;
export const __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
export const __externref_table_dealloc: (a: number) => void;
export const __wbindgen_start: () => void;
