require('dotenv').config();
const RealTimePriceMarketCap = require('./price_mc.js');

async function testPriceAndMarketCap() {
    try {
        console.log(`🧪 Testing Price & Market Cap Calculation`);
        console.log(`========================================`);
        
        const monitor = new RealTimePriceMarketCap();
        
        // Get pool data directly
        const poolData = await monitor.getPoolData(monitor.poolAddress);
        
        if (!poolData) {
            console.log(`❌ Failed to get pool data`);
            return;
        }
        
        console.log(`✅ Pool Data Retrieved:`);
        console.log(`   Type: ${poolData.type}`);
        console.log(`   Address: ${poolData.address}`);
        
        // Calculate price
        const price = await monitor.calculateTokenPrice(monitor.tokenMintAddress, [poolData]);
        
        if (price > 0) {
            // Calculate market cap
            const marketCap = await monitor.calculateMarketCap(monitor.tokenMintAddress, price);
            
            console.log(`\n💰 FINAL RESULTS:`);
            console.log(`   Token: ${monitor.tokenMintAddress}`);
            console.log(`   Price: $${price.toFixed(6)}`);
            console.log(`   Market Cap: $${marketCap.toLocaleString()}`);
            console.log(`   Pool: ${poolData.address} (${poolData.type})`);
            
            // Test multiple calls to show consistency
            console.log(`\n🔄 Testing consistency (3 calls):`);
            for (let i = 1; i <= 3; i++) {
                const testPrice = await monitor.calculateTokenPrice(monitor.tokenMintAddress, [poolData]);
                console.log(`   Call ${i}: $${testPrice.toFixed(6)}`);
            }
            
        } else {
            console.log(`❌ Failed to calculate price`);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testPriceAndMarketCap();
