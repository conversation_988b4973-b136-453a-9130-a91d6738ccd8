// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v6.30.2
// source: geyser.proto
/* eslint-disable */
import { makeGenericClientConstructor, } from "@grpc/grpc-js";
import Long from "long";
import _m0 from "protobufjs/minimal.js";
import { Timestamp } from "./google/protobuf/timestamp.js";
import { BlockHeight, Rewards, Transaction, TransactionError, TransactionStatusMeta, UnixTimestamp, } from "./solana-storage.js";
export const protobufPackage = "geyser";
export var CommitmentLevel;
(function (CommitmentLevel) {
    CommitmentLevel[CommitmentLevel["PROCESSED"] = 0] = "PROCESSED";
    CommitmentLevel[CommitmentLevel["CONFIRMED"] = 1] = "CONFIRMED";
    CommitmentLevel[CommitmentLevel["FINALIZED"] = 2] = "FINALIZED";
    CommitmentLevel[CommitmentLevel["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(CommitmentLevel || (CommitmentLevel = {}));
export function commitmentLevelFromJSON(object) {
    switch (object) {
        case 0:
        case "PROCESSED":
            return CommitmentLevel.PROCESSED;
        case 1:
        case "CONFIRMED":
            return CommitmentLevel.CONFIRMED;
        case 2:
        case "FINALIZED":
            return CommitmentLevel.FINALIZED;
        case -1:
        case "UNRECOGNIZED":
        default:
            return CommitmentLevel.UNRECOGNIZED;
    }
}
export function commitmentLevelToJSON(object) {
    switch (object) {
        case CommitmentLevel.PROCESSED:
            return "PROCESSED";
        case CommitmentLevel.CONFIRMED:
            return "CONFIRMED";
        case CommitmentLevel.FINALIZED:
            return "FINALIZED";
        case CommitmentLevel.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
export var SlotStatus;
(function (SlotStatus) {
    SlotStatus[SlotStatus["SLOT_PROCESSED"] = 0] = "SLOT_PROCESSED";
    SlotStatus[SlotStatus["SLOT_CONFIRMED"] = 1] = "SLOT_CONFIRMED";
    SlotStatus[SlotStatus["SLOT_FINALIZED"] = 2] = "SLOT_FINALIZED";
    SlotStatus[SlotStatus["SLOT_FIRST_SHRED_RECEIVED"] = 3] = "SLOT_FIRST_SHRED_RECEIVED";
    SlotStatus[SlotStatus["SLOT_COMPLETED"] = 4] = "SLOT_COMPLETED";
    SlotStatus[SlotStatus["SLOT_CREATED_BANK"] = 5] = "SLOT_CREATED_BANK";
    SlotStatus[SlotStatus["SLOT_DEAD"] = 6] = "SLOT_DEAD";
    SlotStatus[SlotStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(SlotStatus || (SlotStatus = {}));
export function slotStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "SLOT_PROCESSED":
            return SlotStatus.SLOT_PROCESSED;
        case 1:
        case "SLOT_CONFIRMED":
            return SlotStatus.SLOT_CONFIRMED;
        case 2:
        case "SLOT_FINALIZED":
            return SlotStatus.SLOT_FINALIZED;
        case 3:
        case "SLOT_FIRST_SHRED_RECEIVED":
            return SlotStatus.SLOT_FIRST_SHRED_RECEIVED;
        case 4:
        case "SLOT_COMPLETED":
            return SlotStatus.SLOT_COMPLETED;
        case 5:
        case "SLOT_CREATED_BANK":
            return SlotStatus.SLOT_CREATED_BANK;
        case 6:
        case "SLOT_DEAD":
            return SlotStatus.SLOT_DEAD;
        case -1:
        case "UNRECOGNIZED":
        default:
            return SlotStatus.UNRECOGNIZED;
    }
}
export function slotStatusToJSON(object) {
    switch (object) {
        case SlotStatus.SLOT_PROCESSED:
            return "SLOT_PROCESSED";
        case SlotStatus.SLOT_CONFIRMED:
            return "SLOT_CONFIRMED";
        case SlotStatus.SLOT_FINALIZED:
            return "SLOT_FINALIZED";
        case SlotStatus.SLOT_FIRST_SHRED_RECEIVED:
            return "SLOT_FIRST_SHRED_RECEIVED";
        case SlotStatus.SLOT_COMPLETED:
            return "SLOT_COMPLETED";
        case SlotStatus.SLOT_CREATED_BANK:
            return "SLOT_CREATED_BANK";
        case SlotStatus.SLOT_DEAD:
            return "SLOT_DEAD";
        case SlotStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseSubscribeRequest() {
    return {
        accounts: {},
        slots: {},
        transactions: {},
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        commitment: undefined,
        accountsDataSlice: [],
        ping: undefined,
        fromSlot: undefined,
    };
}
export const SubscribeRequest = {
    encode(message, writer = _m0.Writer.create()) {
        Object.entries(message.accounts).forEach(([key, value]) => {
            SubscribeRequest_AccountsEntry.encode({ key: key, value }, writer.uint32(10).fork()).ldelim();
        });
        Object.entries(message.slots).forEach(([key, value]) => {
            SubscribeRequest_SlotsEntry.encode({ key: key, value }, writer.uint32(18).fork()).ldelim();
        });
        Object.entries(message.transactions).forEach(([key, value]) => {
            SubscribeRequest_TransactionsEntry.encode({ key: key, value }, writer.uint32(26).fork()).ldelim();
        });
        Object.entries(message.transactionsStatus).forEach(([key, value]) => {
            SubscribeRequest_TransactionsStatusEntry.encode({ key: key, value }, writer.uint32(82).fork()).ldelim();
        });
        Object.entries(message.blocks).forEach(([key, value]) => {
            SubscribeRequest_BlocksEntry.encode({ key: key, value }, writer.uint32(34).fork()).ldelim();
        });
        Object.entries(message.blocksMeta).forEach(([key, value]) => {
            SubscribeRequest_BlocksMetaEntry.encode({ key: key, value }, writer.uint32(42).fork()).ldelim();
        });
        Object.entries(message.entry).forEach(([key, value]) => {
            SubscribeRequest_EntryEntry.encode({ key: key, value }, writer.uint32(66).fork()).ldelim();
        });
        if (message.commitment !== undefined) {
            writer.uint32(48).int32(message.commitment);
        }
        for (const v of message.accountsDataSlice) {
            SubscribeRequestAccountsDataSlice.encode(v, writer.uint32(58).fork()).ldelim();
        }
        if (message.ping !== undefined) {
            SubscribeRequestPing.encode(message.ping, writer.uint32(74).fork()).ldelim();
        }
        if (message.fromSlot !== undefined) {
            writer.uint32(88).uint64(message.fromSlot);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    const entry1 = SubscribeRequest_AccountsEntry.decode(reader, reader.uint32());
                    if (entry1.value !== undefined) {
                        message.accounts[entry1.key] = entry1.value;
                    }
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    const entry2 = SubscribeRequest_SlotsEntry.decode(reader, reader.uint32());
                    if (entry2.value !== undefined) {
                        message.slots[entry2.key] = entry2.value;
                    }
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    const entry3 = SubscribeRequest_TransactionsEntry.decode(reader, reader.uint32());
                    if (entry3.value !== undefined) {
                        message.transactions[entry3.key] = entry3.value;
                    }
                    continue;
                case 10:
                    if (tag !== 82) {
                        break;
                    }
                    const entry10 = SubscribeRequest_TransactionsStatusEntry.decode(reader, reader.uint32());
                    if (entry10.value !== undefined) {
                        message.transactionsStatus[entry10.key] = entry10.value;
                    }
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    const entry4 = SubscribeRequest_BlocksEntry.decode(reader, reader.uint32());
                    if (entry4.value !== undefined) {
                        message.blocks[entry4.key] = entry4.value;
                    }
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    const entry5 = SubscribeRequest_BlocksMetaEntry.decode(reader, reader.uint32());
                    if (entry5.value !== undefined) {
                        message.blocksMeta[entry5.key] = entry5.value;
                    }
                    continue;
                case 8:
                    if (tag !== 66) {
                        break;
                    }
                    const entry8 = SubscribeRequest_EntryEntry.decode(reader, reader.uint32());
                    if (entry8.value !== undefined) {
                        message.entry[entry8.key] = entry8.value;
                    }
                    continue;
                case 6:
                    if (tag !== 48) {
                        break;
                    }
                    message.commitment = reader.int32();
                    continue;
                case 7:
                    if (tag !== 58) {
                        break;
                    }
                    message.accountsDataSlice.push(SubscribeRequestAccountsDataSlice.decode(reader, reader.uint32()));
                    continue;
                case 9:
                    if (tag !== 74) {
                        break;
                    }
                    message.ping = SubscribeRequestPing.decode(reader, reader.uint32());
                    continue;
                case 11:
                    if (tag !== 88) {
                        break;
                    }
                    message.fromSlot = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            accounts: isObject(object.accounts)
                ? Object.entries(object.accounts).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterAccounts.fromJSON(value);
                    return acc;
                }, {})
                : {},
            slots: isObject(object.slots)
                ? Object.entries(object.slots).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterSlots.fromJSON(value);
                    return acc;
                }, {})
                : {},
            transactions: isObject(object.transactions)
                ? Object.entries(object.transactions).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterTransactions.fromJSON(value);
                    return acc;
                }, {})
                : {},
            transactionsStatus: isObject(object.transactionsStatus)
                ? Object.entries(object.transactionsStatus).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterTransactions.fromJSON(value);
                    return acc;
                }, {})
                : {},
            blocks: isObject(object.blocks)
                ? Object.entries(object.blocks).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterBlocks.fromJSON(value);
                    return acc;
                }, {})
                : {},
            blocksMeta: isObject(object.blocksMeta)
                ? Object.entries(object.blocksMeta).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterBlocksMeta.fromJSON(value);
                    return acc;
                }, {})
                : {},
            entry: isObject(object.entry)
                ? Object.entries(object.entry).reduce((acc, [key, value]) => {
                    acc[key] = SubscribeRequestFilterEntry.fromJSON(value);
                    return acc;
                }, {})
                : {},
            commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined,
            accountsDataSlice: globalThis.Array.isArray(object?.accountsDataSlice)
                ? object.accountsDataSlice.map((e) => SubscribeRequestAccountsDataSlice.fromJSON(e))
                : [],
            ping: isSet(object.ping) ? SubscribeRequestPing.fromJSON(object.ping) : undefined,
            fromSlot: isSet(object.fromSlot) ? globalThis.String(object.fromSlot) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.accounts) {
            const entries = Object.entries(message.accounts);
            if (entries.length > 0) {
                obj.accounts = {};
                entries.forEach(([k, v]) => {
                    obj.accounts[k] = SubscribeRequestFilterAccounts.toJSON(v);
                });
            }
        }
        if (message.slots) {
            const entries = Object.entries(message.slots);
            if (entries.length > 0) {
                obj.slots = {};
                entries.forEach(([k, v]) => {
                    obj.slots[k] = SubscribeRequestFilterSlots.toJSON(v);
                });
            }
        }
        if (message.transactions) {
            const entries = Object.entries(message.transactions);
            if (entries.length > 0) {
                obj.transactions = {};
                entries.forEach(([k, v]) => {
                    obj.transactions[k] = SubscribeRequestFilterTransactions.toJSON(v);
                });
            }
        }
        if (message.transactionsStatus) {
            const entries = Object.entries(message.transactionsStatus);
            if (entries.length > 0) {
                obj.transactionsStatus = {};
                entries.forEach(([k, v]) => {
                    obj.transactionsStatus[k] = SubscribeRequestFilterTransactions.toJSON(v);
                });
            }
        }
        if (message.blocks) {
            const entries = Object.entries(message.blocks);
            if (entries.length > 0) {
                obj.blocks = {};
                entries.forEach(([k, v]) => {
                    obj.blocks[k] = SubscribeRequestFilterBlocks.toJSON(v);
                });
            }
        }
        if (message.blocksMeta) {
            const entries = Object.entries(message.blocksMeta);
            if (entries.length > 0) {
                obj.blocksMeta = {};
                entries.forEach(([k, v]) => {
                    obj.blocksMeta[k] = SubscribeRequestFilterBlocksMeta.toJSON(v);
                });
            }
        }
        if (message.entry) {
            const entries = Object.entries(message.entry);
            if (entries.length > 0) {
                obj.entry = {};
                entries.forEach(([k, v]) => {
                    obj.entry[k] = SubscribeRequestFilterEntry.toJSON(v);
                });
            }
        }
        if (message.commitment !== undefined) {
            obj.commitment = commitmentLevelToJSON(message.commitment);
        }
        if (message.accountsDataSlice?.length) {
            obj.accountsDataSlice = message.accountsDataSlice.map((e) => SubscribeRequestAccountsDataSlice.toJSON(e));
        }
        if (message.ping !== undefined) {
            obj.ping = SubscribeRequestPing.toJSON(message.ping);
        }
        if (message.fromSlot !== undefined) {
            obj.fromSlot = message.fromSlot;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest();
        message.accounts = Object.entries(object.accounts ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterAccounts.fromPartial(value);
            }
            return acc;
        }, {});
        message.slots = Object.entries(object.slots ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterSlots.fromPartial(value);
            }
            return acc;
        }, {});
        message.transactions = Object.entries(object.transactions ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterTransactions.fromPartial(value);
            }
            return acc;
        }, {});
        message.transactionsStatus = Object.entries(object.transactionsStatus ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterTransactions.fromPartial(value);
            }
            return acc;
        }, {});
        message.blocks = Object.entries(object.blocks ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterBlocks.fromPartial(value);
            }
            return acc;
        }, {});
        message.blocksMeta = Object.entries(object.blocksMeta ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterBlocksMeta.fromPartial(value);
            }
            return acc;
        }, {});
        message.entry = Object.entries(object.entry ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = SubscribeRequestFilterEntry.fromPartial(value);
            }
            return acc;
        }, {});
        message.commitment = object.commitment ?? undefined;
        message.accountsDataSlice =
            object.accountsDataSlice?.map((e) => SubscribeRequestAccountsDataSlice.fromPartial(e)) || [];
        message.ping = (object.ping !== undefined && object.ping !== null)
            ? SubscribeRequestPing.fromPartial(object.ping)
            : undefined;
        message.fromSlot = object.fromSlot ?? undefined;
        return message;
    },
};
function createBaseSubscribeRequest_AccountsEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_AccountsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterAccounts.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_AccountsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterAccounts.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterAccounts.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterAccounts.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_AccountsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_AccountsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterAccounts.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequest_SlotsEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_SlotsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterSlots.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_SlotsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterSlots.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterSlots.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterSlots.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_SlotsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_SlotsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterSlots.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequest_TransactionsEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_TransactionsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterTransactions.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_TransactionsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterTransactions.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterTransactions.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterTransactions.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_TransactionsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_TransactionsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterTransactions.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequest_TransactionsStatusEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_TransactionsStatusEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterTransactions.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_TransactionsStatusEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterTransactions.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterTransactions.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterTransactions.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_TransactionsStatusEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_TransactionsStatusEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterTransactions.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequest_BlocksEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_BlocksEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterBlocks.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_BlocksEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterBlocks.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterBlocks.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterBlocks.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_BlocksEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_BlocksEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterBlocks.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequest_BlocksMetaEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_BlocksMetaEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterBlocksMeta.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_BlocksMetaEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterBlocksMeta.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterBlocksMeta.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterBlocksMeta.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_BlocksMetaEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_BlocksMetaEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterBlocksMeta.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequest_EntryEntry() {
    return { key: "", value: undefined };
}
export const SubscribeRequest_EntryEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            SubscribeRequestFilterEntry.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequest_EntryEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.key = reader.string();
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.value = SubscribeRequestFilterEntry.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? SubscribeRequestFilterEntry.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = SubscribeRequestFilterEntry.toJSON(message.value);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequest_EntryEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequest_EntryEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? SubscribeRequestFilterEntry.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterAccounts() {
    return { account: [], owner: [], filters: [], nonemptyTxnSignature: undefined };
}
export const SubscribeRequestFilterAccounts = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.account) {
            writer.uint32(18).string(v);
        }
        for (const v of message.owner) {
            writer.uint32(26).string(v);
        }
        for (const v of message.filters) {
            SubscribeRequestFilterAccountsFilter.encode(v, writer.uint32(34).fork()).ldelim();
        }
        if (message.nonemptyTxnSignature !== undefined) {
            writer.uint32(40).bool(message.nonemptyTxnSignature);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterAccounts();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.account.push(reader.string());
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.owner.push(reader.string());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.filters.push(SubscribeRequestFilterAccountsFilter.decode(reader, reader.uint32()));
                    continue;
                case 5:
                    if (tag !== 40) {
                        break;
                    }
                    message.nonemptyTxnSignature = reader.bool();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: globalThis.Array.isArray(object?.account) ? object.account.map((e) => globalThis.String(e)) : [],
            owner: globalThis.Array.isArray(object?.owner) ? object.owner.map((e) => globalThis.String(e)) : [],
            filters: globalThis.Array.isArray(object?.filters)
                ? object.filters.map((e) => SubscribeRequestFilterAccountsFilter.fromJSON(e))
                : [],
            nonemptyTxnSignature: isSet(object.nonemptyTxnSignature)
                ? globalThis.Boolean(object.nonemptyTxnSignature)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.account?.length) {
            obj.account = message.account;
        }
        if (message.owner?.length) {
            obj.owner = message.owner;
        }
        if (message.filters?.length) {
            obj.filters = message.filters.map((e) => SubscribeRequestFilterAccountsFilter.toJSON(e));
        }
        if (message.nonemptyTxnSignature !== undefined) {
            obj.nonemptyTxnSignature = message.nonemptyTxnSignature;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterAccounts.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterAccounts();
        message.account = object.account?.map((e) => e) || [];
        message.owner = object.owner?.map((e) => e) || [];
        message.filters = object.filters?.map((e) => SubscribeRequestFilterAccountsFilter.fromPartial(e)) || [];
        message.nonemptyTxnSignature = object.nonemptyTxnSignature ?? undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterAccountsFilter() {
    return { memcmp: undefined, datasize: undefined, tokenAccountState: undefined, lamports: undefined };
}
export const SubscribeRequestFilterAccountsFilter = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.memcmp !== undefined) {
            SubscribeRequestFilterAccountsFilterMemcmp.encode(message.memcmp, writer.uint32(10).fork()).ldelim();
        }
        if (message.datasize !== undefined) {
            writer.uint32(16).uint64(message.datasize);
        }
        if (message.tokenAccountState !== undefined) {
            writer.uint32(24).bool(message.tokenAccountState);
        }
        if (message.lamports !== undefined) {
            SubscribeRequestFilterAccountsFilterLamports.encode(message.lamports, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterAccountsFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.memcmp = SubscribeRequestFilterAccountsFilterMemcmp.decode(reader, reader.uint32());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.datasize = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.tokenAccountState = reader.bool();
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.lamports = SubscribeRequestFilterAccountsFilterLamports.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            memcmp: isSet(object.memcmp) ? SubscribeRequestFilterAccountsFilterMemcmp.fromJSON(object.memcmp) : undefined,
            datasize: isSet(object.datasize) ? globalThis.String(object.datasize) : undefined,
            tokenAccountState: isSet(object.tokenAccountState) ? globalThis.Boolean(object.tokenAccountState) : undefined,
            lamports: isSet(object.lamports)
                ? SubscribeRequestFilterAccountsFilterLamports.fromJSON(object.lamports)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.memcmp !== undefined) {
            obj.memcmp = SubscribeRequestFilterAccountsFilterMemcmp.toJSON(message.memcmp);
        }
        if (message.datasize !== undefined) {
            obj.datasize = message.datasize;
        }
        if (message.tokenAccountState !== undefined) {
            obj.tokenAccountState = message.tokenAccountState;
        }
        if (message.lamports !== undefined) {
            obj.lamports = SubscribeRequestFilterAccountsFilterLamports.toJSON(message.lamports);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterAccountsFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterAccountsFilter();
        message.memcmp = (object.memcmp !== undefined && object.memcmp !== null)
            ? SubscribeRequestFilterAccountsFilterMemcmp.fromPartial(object.memcmp)
            : undefined;
        message.datasize = object.datasize ?? undefined;
        message.tokenAccountState = object.tokenAccountState ?? undefined;
        message.lamports = (object.lamports !== undefined && object.lamports !== null)
            ? SubscribeRequestFilterAccountsFilterLamports.fromPartial(object.lamports)
            : undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterAccountsFilterMemcmp() {
    return { offset: "0", bytes: undefined, base58: undefined, base64: undefined };
}
export const SubscribeRequestFilterAccountsFilterMemcmp = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.offset !== "0") {
            writer.uint32(8).uint64(message.offset);
        }
        if (message.bytes !== undefined) {
            writer.uint32(18).bytes(message.bytes);
        }
        if (message.base58 !== undefined) {
            writer.uint32(26).string(message.base58);
        }
        if (message.base64 !== undefined) {
            writer.uint32(34).string(message.base64);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterAccountsFilterMemcmp();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.offset = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.bytes = reader.bytes();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.base58 = reader.string();
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.base64 = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            offset: isSet(object.offset) ? globalThis.String(object.offset) : "0",
            bytes: isSet(object.bytes) ? bytesFromBase64(object.bytes) : undefined,
            base58: isSet(object.base58) ? globalThis.String(object.base58) : undefined,
            base64: isSet(object.base64) ? globalThis.String(object.base64) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.offset !== "0") {
            obj.offset = message.offset;
        }
        if (message.bytes !== undefined) {
            obj.bytes = base64FromBytes(message.bytes);
        }
        if (message.base58 !== undefined) {
            obj.base58 = message.base58;
        }
        if (message.base64 !== undefined) {
            obj.base64 = message.base64;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterAccountsFilterMemcmp.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterAccountsFilterMemcmp();
        message.offset = object.offset ?? "0";
        message.bytes = object.bytes ?? undefined;
        message.base58 = object.base58 ?? undefined;
        message.base64 = object.base64 ?? undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterAccountsFilterLamports() {
    return { eq: undefined, ne: undefined, lt: undefined, gt: undefined };
}
export const SubscribeRequestFilterAccountsFilterLamports = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.eq !== undefined) {
            writer.uint32(8).uint64(message.eq);
        }
        if (message.ne !== undefined) {
            writer.uint32(16).uint64(message.ne);
        }
        if (message.lt !== undefined) {
            writer.uint32(24).uint64(message.lt);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).uint64(message.gt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterAccountsFilterLamports();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.eq = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.ne = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.lt = longToString(reader.uint64());
                    continue;
                case 4:
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            eq: isSet(object.eq) ? globalThis.String(object.eq) : undefined,
            ne: isSet(object.ne) ? globalThis.String(object.ne) : undefined,
            lt: isSet(object.lt) ? globalThis.String(object.lt) : undefined,
            gt: isSet(object.gt) ? globalThis.String(object.gt) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.eq !== undefined) {
            obj.eq = message.eq;
        }
        if (message.ne !== undefined) {
            obj.ne = message.ne;
        }
        if (message.lt !== undefined) {
            obj.lt = message.lt;
        }
        if (message.gt !== undefined) {
            obj.gt = message.gt;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterAccountsFilterLamports.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterAccountsFilterLamports();
        message.eq = object.eq ?? undefined;
        message.ne = object.ne ?? undefined;
        message.lt = object.lt ?? undefined;
        message.gt = object.gt ?? undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterSlots() {
    return { filterByCommitment: undefined, interslotUpdates: undefined };
}
export const SubscribeRequestFilterSlots = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.filterByCommitment !== undefined) {
            writer.uint32(8).bool(message.filterByCommitment);
        }
        if (message.interslotUpdates !== undefined) {
            writer.uint32(16).bool(message.interslotUpdates);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterSlots();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.filterByCommitment = reader.bool();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.interslotUpdates = reader.bool();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            filterByCommitment: isSet(object.filterByCommitment) ? globalThis.Boolean(object.filterByCommitment) : undefined,
            interslotUpdates: isSet(object.interslotUpdates) ? globalThis.Boolean(object.interslotUpdates) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.filterByCommitment !== undefined) {
            obj.filterByCommitment = message.filterByCommitment;
        }
        if (message.interslotUpdates !== undefined) {
            obj.interslotUpdates = message.interslotUpdates;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterSlots.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterSlots();
        message.filterByCommitment = object.filterByCommitment ?? undefined;
        message.interslotUpdates = object.interslotUpdates ?? undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterTransactions() {
    return {
        vote: undefined,
        failed: undefined,
        signature: undefined,
        accountInclude: [],
        accountExclude: [],
        accountRequired: [],
    };
}
export const SubscribeRequestFilterTransactions = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.vote !== undefined) {
            writer.uint32(8).bool(message.vote);
        }
        if (message.failed !== undefined) {
            writer.uint32(16).bool(message.failed);
        }
        if (message.signature !== undefined) {
            writer.uint32(42).string(message.signature);
        }
        for (const v of message.accountInclude) {
            writer.uint32(26).string(v);
        }
        for (const v of message.accountExclude) {
            writer.uint32(34).string(v);
        }
        for (const v of message.accountRequired) {
            writer.uint32(50).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterTransactions();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.vote = reader.bool();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.failed = reader.bool();
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.signature = reader.string();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.accountInclude.push(reader.string());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.accountExclude.push(reader.string());
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.accountRequired.push(reader.string());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            vote: isSet(object.vote) ? globalThis.Boolean(object.vote) : undefined,
            failed: isSet(object.failed) ? globalThis.Boolean(object.failed) : undefined,
            signature: isSet(object.signature) ? globalThis.String(object.signature) : undefined,
            accountInclude: globalThis.Array.isArray(object?.accountInclude)
                ? object.accountInclude.map((e) => globalThis.String(e))
                : [],
            accountExclude: globalThis.Array.isArray(object?.accountExclude)
                ? object.accountExclude.map((e) => globalThis.String(e))
                : [],
            accountRequired: globalThis.Array.isArray(object?.accountRequired)
                ? object.accountRequired.map((e) => globalThis.String(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.vote !== undefined) {
            obj.vote = message.vote;
        }
        if (message.failed !== undefined) {
            obj.failed = message.failed;
        }
        if (message.signature !== undefined) {
            obj.signature = message.signature;
        }
        if (message.accountInclude?.length) {
            obj.accountInclude = message.accountInclude;
        }
        if (message.accountExclude?.length) {
            obj.accountExclude = message.accountExclude;
        }
        if (message.accountRequired?.length) {
            obj.accountRequired = message.accountRequired;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterTransactions.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterTransactions();
        message.vote = object.vote ?? undefined;
        message.failed = object.failed ?? undefined;
        message.signature = object.signature ?? undefined;
        message.accountInclude = object.accountInclude?.map((e) => e) || [];
        message.accountExclude = object.accountExclude?.map((e) => e) || [];
        message.accountRequired = object.accountRequired?.map((e) => e) || [];
        return message;
    },
};
function createBaseSubscribeRequestFilterBlocks() {
    return { accountInclude: [], includeTransactions: undefined, includeAccounts: undefined, includeEntries: undefined };
}
export const SubscribeRequestFilterBlocks = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.accountInclude) {
            writer.uint32(10).string(v);
        }
        if (message.includeTransactions !== undefined) {
            writer.uint32(16).bool(message.includeTransactions);
        }
        if (message.includeAccounts !== undefined) {
            writer.uint32(24).bool(message.includeAccounts);
        }
        if (message.includeEntries !== undefined) {
            writer.uint32(32).bool(message.includeEntries);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterBlocks();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.accountInclude.push(reader.string());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.includeTransactions = reader.bool();
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.includeAccounts = reader.bool();
                    continue;
                case 4:
                    if (tag !== 32) {
                        break;
                    }
                    message.includeEntries = reader.bool();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountInclude: globalThis.Array.isArray(object?.accountInclude)
                ? object.accountInclude.map((e) => globalThis.String(e))
                : [],
            includeTransactions: isSet(object.includeTransactions)
                ? globalThis.Boolean(object.includeTransactions)
                : undefined,
            includeAccounts: isSet(object.includeAccounts) ? globalThis.Boolean(object.includeAccounts) : undefined,
            includeEntries: isSet(object.includeEntries) ? globalThis.Boolean(object.includeEntries) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.accountInclude?.length) {
            obj.accountInclude = message.accountInclude;
        }
        if (message.includeTransactions !== undefined) {
            obj.includeTransactions = message.includeTransactions;
        }
        if (message.includeAccounts !== undefined) {
            obj.includeAccounts = message.includeAccounts;
        }
        if (message.includeEntries !== undefined) {
            obj.includeEntries = message.includeEntries;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterBlocks.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestFilterBlocks();
        message.accountInclude = object.accountInclude?.map((e) => e) || [];
        message.includeTransactions = object.includeTransactions ?? undefined;
        message.includeAccounts = object.includeAccounts ?? undefined;
        message.includeEntries = object.includeEntries ?? undefined;
        return message;
    },
};
function createBaseSubscribeRequestFilterBlocksMeta() {
    return {};
}
export const SubscribeRequestFilterBlocksMeta = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterBlocksMeta();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterBlocksMeta.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSubscribeRequestFilterBlocksMeta();
        return message;
    },
};
function createBaseSubscribeRequestFilterEntry() {
    return {};
}
export const SubscribeRequestFilterEntry = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestFilterEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SubscribeRequestFilterEntry.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSubscribeRequestFilterEntry();
        return message;
    },
};
function createBaseSubscribeRequestAccountsDataSlice() {
    return { offset: "0", length: "0" };
}
export const SubscribeRequestAccountsDataSlice = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.offset !== "0") {
            writer.uint32(8).uint64(message.offset);
        }
        if (message.length !== "0") {
            writer.uint32(16).uint64(message.length);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestAccountsDataSlice();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.offset = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.length = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            offset: isSet(object.offset) ? globalThis.String(object.offset) : "0",
            length: isSet(object.length) ? globalThis.String(object.length) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.offset !== "0") {
            obj.offset = message.offset;
        }
        if (message.length !== "0") {
            obj.length = message.length;
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestAccountsDataSlice.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestAccountsDataSlice();
        message.offset = object.offset ?? "0";
        message.length = object.length ?? "0";
        return message;
    },
};
function createBaseSubscribeRequestPing() {
    return { id: 0 };
}
export const SubscribeRequestPing = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== 0) {
            writer.uint32(8).int32(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeRequestPing();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.id = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== 0) {
            obj.id = Math.round(message.id);
        }
        return obj;
    },
    create(base) {
        return SubscribeRequestPing.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeRequestPing();
        message.id = object.id ?? 0;
        return message;
    },
};
function createBaseSubscribeUpdate() {
    return {
        filters: [],
        account: undefined,
        slot: undefined,
        transaction: undefined,
        transactionStatus: undefined,
        block: undefined,
        ping: undefined,
        pong: undefined,
        blockMeta: undefined,
        entry: undefined,
        createdAt: undefined,
    };
}
export const SubscribeUpdate = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.filters) {
            writer.uint32(10).string(v);
        }
        if (message.account !== undefined) {
            SubscribeUpdateAccount.encode(message.account, writer.uint32(18).fork()).ldelim();
        }
        if (message.slot !== undefined) {
            SubscribeUpdateSlot.encode(message.slot, writer.uint32(26).fork()).ldelim();
        }
        if (message.transaction !== undefined) {
            SubscribeUpdateTransaction.encode(message.transaction, writer.uint32(34).fork()).ldelim();
        }
        if (message.transactionStatus !== undefined) {
            SubscribeUpdateTransactionStatus.encode(message.transactionStatus, writer.uint32(82).fork()).ldelim();
        }
        if (message.block !== undefined) {
            SubscribeUpdateBlock.encode(message.block, writer.uint32(42).fork()).ldelim();
        }
        if (message.ping !== undefined) {
            SubscribeUpdatePing.encode(message.ping, writer.uint32(50).fork()).ldelim();
        }
        if (message.pong !== undefined) {
            SubscribeUpdatePong.encode(message.pong, writer.uint32(74).fork()).ldelim();
        }
        if (message.blockMeta !== undefined) {
            SubscribeUpdateBlockMeta.encode(message.blockMeta, writer.uint32(58).fork()).ldelim();
        }
        if (message.entry !== undefined) {
            SubscribeUpdateEntry.encode(message.entry, writer.uint32(66).fork()).ldelim();
        }
        if (message.createdAt !== undefined) {
            Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(90).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdate();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.filters.push(reader.string());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.account = SubscribeUpdateAccount.decode(reader, reader.uint32());
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.slot = SubscribeUpdateSlot.decode(reader, reader.uint32());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.transaction = SubscribeUpdateTransaction.decode(reader, reader.uint32());
                    continue;
                case 10:
                    if (tag !== 82) {
                        break;
                    }
                    message.transactionStatus = SubscribeUpdateTransactionStatus.decode(reader, reader.uint32());
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.block = SubscribeUpdateBlock.decode(reader, reader.uint32());
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.ping = SubscribeUpdatePing.decode(reader, reader.uint32());
                    continue;
                case 9:
                    if (tag !== 74) {
                        break;
                    }
                    message.pong = SubscribeUpdatePong.decode(reader, reader.uint32());
                    continue;
                case 7:
                    if (tag !== 58) {
                        break;
                    }
                    message.blockMeta = SubscribeUpdateBlockMeta.decode(reader, reader.uint32());
                    continue;
                case 8:
                    if (tag !== 66) {
                        break;
                    }
                    message.entry = SubscribeUpdateEntry.decode(reader, reader.uint32());
                    continue;
                case 11:
                    if (tag !== 90) {
                        break;
                    }
                    message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            filters: globalThis.Array.isArray(object?.filters) ? object.filters.map((e) => globalThis.String(e)) : [],
            account: isSet(object.account) ? SubscribeUpdateAccount.fromJSON(object.account) : undefined,
            slot: isSet(object.slot) ? SubscribeUpdateSlot.fromJSON(object.slot) : undefined,
            transaction: isSet(object.transaction) ? SubscribeUpdateTransaction.fromJSON(object.transaction) : undefined,
            transactionStatus: isSet(object.transactionStatus)
                ? SubscribeUpdateTransactionStatus.fromJSON(object.transactionStatus)
                : undefined,
            block: isSet(object.block) ? SubscribeUpdateBlock.fromJSON(object.block) : undefined,
            ping: isSet(object.ping) ? SubscribeUpdatePing.fromJSON(object.ping) : undefined,
            pong: isSet(object.pong) ? SubscribeUpdatePong.fromJSON(object.pong) : undefined,
            blockMeta: isSet(object.blockMeta) ? SubscribeUpdateBlockMeta.fromJSON(object.blockMeta) : undefined,
            entry: isSet(object.entry) ? SubscribeUpdateEntry.fromJSON(object.entry) : undefined,
            createdAt: isSet(object.createdAt) ? fromJsonTimestamp(object.createdAt) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.filters?.length) {
            obj.filters = message.filters;
        }
        if (message.account !== undefined) {
            obj.account = SubscribeUpdateAccount.toJSON(message.account);
        }
        if (message.slot !== undefined) {
            obj.slot = SubscribeUpdateSlot.toJSON(message.slot);
        }
        if (message.transaction !== undefined) {
            obj.transaction = SubscribeUpdateTransaction.toJSON(message.transaction);
        }
        if (message.transactionStatus !== undefined) {
            obj.transactionStatus = SubscribeUpdateTransactionStatus.toJSON(message.transactionStatus);
        }
        if (message.block !== undefined) {
            obj.block = SubscribeUpdateBlock.toJSON(message.block);
        }
        if (message.ping !== undefined) {
            obj.ping = SubscribeUpdatePing.toJSON(message.ping);
        }
        if (message.pong !== undefined) {
            obj.pong = SubscribeUpdatePong.toJSON(message.pong);
        }
        if (message.blockMeta !== undefined) {
            obj.blockMeta = SubscribeUpdateBlockMeta.toJSON(message.blockMeta);
        }
        if (message.entry !== undefined) {
            obj.entry = SubscribeUpdateEntry.toJSON(message.entry);
        }
        if (message.createdAt !== undefined) {
            obj.createdAt = message.createdAt.toISOString();
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdate.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdate();
        message.filters = object.filters?.map((e) => e) || [];
        message.account = (object.account !== undefined && object.account !== null)
            ? SubscribeUpdateAccount.fromPartial(object.account)
            : undefined;
        message.slot = (object.slot !== undefined && object.slot !== null)
            ? SubscribeUpdateSlot.fromPartial(object.slot)
            : undefined;
        message.transaction = (object.transaction !== undefined && object.transaction !== null)
            ? SubscribeUpdateTransaction.fromPartial(object.transaction)
            : undefined;
        message.transactionStatus = (object.transactionStatus !== undefined && object.transactionStatus !== null)
            ? SubscribeUpdateTransactionStatus.fromPartial(object.transactionStatus)
            : undefined;
        message.block = (object.block !== undefined && object.block !== null)
            ? SubscribeUpdateBlock.fromPartial(object.block)
            : undefined;
        message.ping = (object.ping !== undefined && object.ping !== null)
            ? SubscribeUpdatePing.fromPartial(object.ping)
            : undefined;
        message.pong = (object.pong !== undefined && object.pong !== null)
            ? SubscribeUpdatePong.fromPartial(object.pong)
            : undefined;
        message.blockMeta = (object.blockMeta !== undefined && object.blockMeta !== null)
            ? SubscribeUpdateBlockMeta.fromPartial(object.blockMeta)
            : undefined;
        message.entry = (object.entry !== undefined && object.entry !== null)
            ? SubscribeUpdateEntry.fromPartial(object.entry)
            : undefined;
        message.createdAt = object.createdAt ?? undefined;
        return message;
    },
};
function createBaseSubscribeUpdateAccount() {
    return { account: undefined, slot: "0", isStartup: false };
}
export const SubscribeUpdateAccount = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.account !== undefined) {
            SubscribeUpdateAccountInfo.encode(message.account, writer.uint32(10).fork()).ldelim();
        }
        if (message.slot !== "0") {
            writer.uint32(16).uint64(message.slot);
        }
        if (message.isStartup !== false) {
            writer.uint32(24).bool(message.isStartup);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateAccount();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.account = SubscribeUpdateAccountInfo.decode(reader, reader.uint32());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.isStartup = reader.bool();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? SubscribeUpdateAccountInfo.fromJSON(object.account) : undefined,
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            isStartup: isSet(object.isStartup) ? globalThis.Boolean(object.isStartup) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.account !== undefined) {
            obj.account = SubscribeUpdateAccountInfo.toJSON(message.account);
        }
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.isStartup !== false) {
            obj.isStartup = message.isStartup;
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateAccount.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateAccount();
        message.account = (object.account !== undefined && object.account !== null)
            ? SubscribeUpdateAccountInfo.fromPartial(object.account)
            : undefined;
        message.slot = object.slot ?? "0";
        message.isStartup = object.isStartup ?? false;
        return message;
    },
};
function createBaseSubscribeUpdateAccountInfo() {
    return {
        pubkey: new Uint8Array(0),
        lamports: "0",
        owner: new Uint8Array(0),
        executable: false,
        rentEpoch: "0",
        data: new Uint8Array(0),
        writeVersion: "0",
        txnSignature: undefined,
    };
}
export const SubscribeUpdateAccountInfo = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.pubkey.length !== 0) {
            writer.uint32(10).bytes(message.pubkey);
        }
        if (message.lamports !== "0") {
            writer.uint32(16).uint64(message.lamports);
        }
        if (message.owner.length !== 0) {
            writer.uint32(26).bytes(message.owner);
        }
        if (message.executable !== false) {
            writer.uint32(32).bool(message.executable);
        }
        if (message.rentEpoch !== "0") {
            writer.uint32(40).uint64(message.rentEpoch);
        }
        if (message.data.length !== 0) {
            writer.uint32(50).bytes(message.data);
        }
        if (message.writeVersion !== "0") {
            writer.uint32(56).uint64(message.writeVersion);
        }
        if (message.txnSignature !== undefined) {
            writer.uint32(66).bytes(message.txnSignature);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateAccountInfo();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.pubkey = reader.bytes();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.lamports = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.owner = reader.bytes();
                    continue;
                case 4:
                    if (tag !== 32) {
                        break;
                    }
                    message.executable = reader.bool();
                    continue;
                case 5:
                    if (tag !== 40) {
                        break;
                    }
                    message.rentEpoch = longToString(reader.uint64());
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.data = reader.bytes();
                    continue;
                case 7:
                    if (tag !== 56) {
                        break;
                    }
                    message.writeVersion = longToString(reader.uint64());
                    continue;
                case 8:
                    if (tag !== 66) {
                        break;
                    }
                    message.txnSignature = reader.bytes();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            pubkey: isSet(object.pubkey) ? bytesFromBase64(object.pubkey) : new Uint8Array(0),
            lamports: isSet(object.lamports) ? globalThis.String(object.lamports) : "0",
            owner: isSet(object.owner) ? bytesFromBase64(object.owner) : new Uint8Array(0),
            executable: isSet(object.executable) ? globalThis.Boolean(object.executable) : false,
            rentEpoch: isSet(object.rentEpoch) ? globalThis.String(object.rentEpoch) : "0",
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0),
            writeVersion: isSet(object.writeVersion) ? globalThis.String(object.writeVersion) : "0",
            txnSignature: isSet(object.txnSignature) ? bytesFromBase64(object.txnSignature) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.pubkey.length !== 0) {
            obj.pubkey = base64FromBytes(message.pubkey);
        }
        if (message.lamports !== "0") {
            obj.lamports = message.lamports;
        }
        if (message.owner.length !== 0) {
            obj.owner = base64FromBytes(message.owner);
        }
        if (message.executable !== false) {
            obj.executable = message.executable;
        }
        if (message.rentEpoch !== "0") {
            obj.rentEpoch = message.rentEpoch;
        }
        if (message.data.length !== 0) {
            obj.data = base64FromBytes(message.data);
        }
        if (message.writeVersion !== "0") {
            obj.writeVersion = message.writeVersion;
        }
        if (message.txnSignature !== undefined) {
            obj.txnSignature = base64FromBytes(message.txnSignature);
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateAccountInfo.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateAccountInfo();
        message.pubkey = object.pubkey ?? new Uint8Array(0);
        message.lamports = object.lamports ?? "0";
        message.owner = object.owner ?? new Uint8Array(0);
        message.executable = object.executable ?? false;
        message.rentEpoch = object.rentEpoch ?? "0";
        message.data = object.data ?? new Uint8Array(0);
        message.writeVersion = object.writeVersion ?? "0";
        message.txnSignature = object.txnSignature ?? undefined;
        return message;
    },
};
function createBaseSubscribeUpdateSlot() {
    return { slot: "0", parent: undefined, status: 0, deadError: undefined };
}
export const SubscribeUpdateSlot = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.parent !== undefined) {
            writer.uint32(16).uint64(message.parent);
        }
        if (message.status !== 0) {
            writer.uint32(24).int32(message.status);
        }
        if (message.deadError !== undefined) {
            writer.uint32(34).string(message.deadError);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateSlot();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.parent = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.status = reader.int32();
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.deadError = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            parent: isSet(object.parent) ? globalThis.String(object.parent) : undefined,
            status: isSet(object.status) ? slotStatusFromJSON(object.status) : 0,
            deadError: isSet(object.deadError) ? globalThis.String(object.deadError) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.parent !== undefined) {
            obj.parent = message.parent;
        }
        if (message.status !== 0) {
            obj.status = slotStatusToJSON(message.status);
        }
        if (message.deadError !== undefined) {
            obj.deadError = message.deadError;
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateSlot.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateSlot();
        message.slot = object.slot ?? "0";
        message.parent = object.parent ?? undefined;
        message.status = object.status ?? 0;
        message.deadError = object.deadError ?? undefined;
        return message;
    },
};
function createBaseSubscribeUpdateTransaction() {
    return { transaction: undefined, slot: "0" };
}
export const SubscribeUpdateTransaction = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.transaction !== undefined) {
            SubscribeUpdateTransactionInfo.encode(message.transaction, writer.uint32(10).fork()).ldelim();
        }
        if (message.slot !== "0") {
            writer.uint32(16).uint64(message.slot);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateTransaction();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.transaction = SubscribeUpdateTransactionInfo.decode(reader, reader.uint32());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            transaction: isSet(object.transaction) ? SubscribeUpdateTransactionInfo.fromJSON(object.transaction) : undefined,
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.transaction !== undefined) {
            obj.transaction = SubscribeUpdateTransactionInfo.toJSON(message.transaction);
        }
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateTransaction.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateTransaction();
        message.transaction = (object.transaction !== undefined && object.transaction !== null)
            ? SubscribeUpdateTransactionInfo.fromPartial(object.transaction)
            : undefined;
        message.slot = object.slot ?? "0";
        return message;
    },
};
function createBaseSubscribeUpdateTransactionInfo() {
    return { signature: new Uint8Array(0), isVote: false, transaction: undefined, meta: undefined, index: "0" };
}
export const SubscribeUpdateTransactionInfo = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.signature.length !== 0) {
            writer.uint32(10).bytes(message.signature);
        }
        if (message.isVote !== false) {
            writer.uint32(16).bool(message.isVote);
        }
        if (message.transaction !== undefined) {
            Transaction.encode(message.transaction, writer.uint32(26).fork()).ldelim();
        }
        if (message.meta !== undefined) {
            TransactionStatusMeta.encode(message.meta, writer.uint32(34).fork()).ldelim();
        }
        if (message.index !== "0") {
            writer.uint32(40).uint64(message.index);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateTransactionInfo();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.signature = reader.bytes();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.isVote = reader.bool();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.transaction = Transaction.decode(reader, reader.uint32());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.meta = TransactionStatusMeta.decode(reader, reader.uint32());
                    continue;
                case 5:
                    if (tag !== 40) {
                        break;
                    }
                    message.index = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            signature: isSet(object.signature) ? bytesFromBase64(object.signature) : new Uint8Array(0),
            isVote: isSet(object.isVote) ? globalThis.Boolean(object.isVote) : false,
            transaction: isSet(object.transaction) ? Transaction.fromJSON(object.transaction) : undefined,
            meta: isSet(object.meta) ? TransactionStatusMeta.fromJSON(object.meta) : undefined,
            index: isSet(object.index) ? globalThis.String(object.index) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.signature.length !== 0) {
            obj.signature = base64FromBytes(message.signature);
        }
        if (message.isVote !== false) {
            obj.isVote = message.isVote;
        }
        if (message.transaction !== undefined) {
            obj.transaction = Transaction.toJSON(message.transaction);
        }
        if (message.meta !== undefined) {
            obj.meta = TransactionStatusMeta.toJSON(message.meta);
        }
        if (message.index !== "0") {
            obj.index = message.index;
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateTransactionInfo.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateTransactionInfo();
        message.signature = object.signature ?? new Uint8Array(0);
        message.isVote = object.isVote ?? false;
        message.transaction = (object.transaction !== undefined && object.transaction !== null)
            ? Transaction.fromPartial(object.transaction)
            : undefined;
        message.meta = (object.meta !== undefined && object.meta !== null)
            ? TransactionStatusMeta.fromPartial(object.meta)
            : undefined;
        message.index = object.index ?? "0";
        return message;
    },
};
function createBaseSubscribeUpdateTransactionStatus() {
    return { slot: "0", signature: new Uint8Array(0), isVote: false, index: "0", err: undefined };
}
export const SubscribeUpdateTransactionStatus = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.signature.length !== 0) {
            writer.uint32(18).bytes(message.signature);
        }
        if (message.isVote !== false) {
            writer.uint32(24).bool(message.isVote);
        }
        if (message.index !== "0") {
            writer.uint32(32).uint64(message.index);
        }
        if (message.err !== undefined) {
            TransactionError.encode(message.err, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateTransactionStatus();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.signature = reader.bytes();
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.isVote = reader.bool();
                    continue;
                case 4:
                    if (tag !== 32) {
                        break;
                    }
                    message.index = longToString(reader.uint64());
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.err = TransactionError.decode(reader, reader.uint32());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            signature: isSet(object.signature) ? bytesFromBase64(object.signature) : new Uint8Array(0),
            isVote: isSet(object.isVote) ? globalThis.Boolean(object.isVote) : false,
            index: isSet(object.index) ? globalThis.String(object.index) : "0",
            err: isSet(object.err) ? TransactionError.fromJSON(object.err) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.signature.length !== 0) {
            obj.signature = base64FromBytes(message.signature);
        }
        if (message.isVote !== false) {
            obj.isVote = message.isVote;
        }
        if (message.index !== "0") {
            obj.index = message.index;
        }
        if (message.err !== undefined) {
            obj.err = TransactionError.toJSON(message.err);
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateTransactionStatus.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateTransactionStatus();
        message.slot = object.slot ?? "0";
        message.signature = object.signature ?? new Uint8Array(0);
        message.isVote = object.isVote ?? false;
        message.index = object.index ?? "0";
        message.err = (object.err !== undefined && object.err !== null)
            ? TransactionError.fromPartial(object.err)
            : undefined;
        return message;
    },
};
function createBaseSubscribeUpdateBlock() {
    return {
        slot: "0",
        blockhash: "",
        rewards: undefined,
        blockTime: undefined,
        blockHeight: undefined,
        parentSlot: "0",
        parentBlockhash: "",
        executedTransactionCount: "0",
        transactions: [],
        updatedAccountCount: "0",
        accounts: [],
        entriesCount: "0",
        entries: [],
    };
}
export const SubscribeUpdateBlock = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.blockhash !== "") {
            writer.uint32(18).string(message.blockhash);
        }
        if (message.rewards !== undefined) {
            Rewards.encode(message.rewards, writer.uint32(26).fork()).ldelim();
        }
        if (message.blockTime !== undefined) {
            UnixTimestamp.encode(message.blockTime, writer.uint32(34).fork()).ldelim();
        }
        if (message.blockHeight !== undefined) {
            BlockHeight.encode(message.blockHeight, writer.uint32(42).fork()).ldelim();
        }
        if (message.parentSlot !== "0") {
            writer.uint32(56).uint64(message.parentSlot);
        }
        if (message.parentBlockhash !== "") {
            writer.uint32(66).string(message.parentBlockhash);
        }
        if (message.executedTransactionCount !== "0") {
            writer.uint32(72).uint64(message.executedTransactionCount);
        }
        for (const v of message.transactions) {
            SubscribeUpdateTransactionInfo.encode(v, writer.uint32(50).fork()).ldelim();
        }
        if (message.updatedAccountCount !== "0") {
            writer.uint32(80).uint64(message.updatedAccountCount);
        }
        for (const v of message.accounts) {
            SubscribeUpdateAccountInfo.encode(v, writer.uint32(90).fork()).ldelim();
        }
        if (message.entriesCount !== "0") {
            writer.uint32(96).uint64(message.entriesCount);
        }
        for (const v of message.entries) {
            SubscribeUpdateEntry.encode(v, writer.uint32(106).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateBlock();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.blockhash = reader.string();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.rewards = Rewards.decode(reader, reader.uint32());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.blockTime = UnixTimestamp.decode(reader, reader.uint32());
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.blockHeight = BlockHeight.decode(reader, reader.uint32());
                    continue;
                case 7:
                    if (tag !== 56) {
                        break;
                    }
                    message.parentSlot = longToString(reader.uint64());
                    continue;
                case 8:
                    if (tag !== 66) {
                        break;
                    }
                    message.parentBlockhash = reader.string();
                    continue;
                case 9:
                    if (tag !== 72) {
                        break;
                    }
                    message.executedTransactionCount = longToString(reader.uint64());
                    continue;
                case 6:
                    if (tag !== 50) {
                        break;
                    }
                    message.transactions.push(SubscribeUpdateTransactionInfo.decode(reader, reader.uint32()));
                    continue;
                case 10:
                    if (tag !== 80) {
                        break;
                    }
                    message.updatedAccountCount = longToString(reader.uint64());
                    continue;
                case 11:
                    if (tag !== 90) {
                        break;
                    }
                    message.accounts.push(SubscribeUpdateAccountInfo.decode(reader, reader.uint32()));
                    continue;
                case 12:
                    if (tag !== 96) {
                        break;
                    }
                    message.entriesCount = longToString(reader.uint64());
                    continue;
                case 13:
                    if (tag !== 106) {
                        break;
                    }
                    message.entries.push(SubscribeUpdateEntry.decode(reader, reader.uint32()));
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
            rewards: isSet(object.rewards) ? Rewards.fromJSON(object.rewards) : undefined,
            blockTime: isSet(object.blockTime) ? UnixTimestamp.fromJSON(object.blockTime) : undefined,
            blockHeight: isSet(object.blockHeight) ? BlockHeight.fromJSON(object.blockHeight) : undefined,
            parentSlot: isSet(object.parentSlot) ? globalThis.String(object.parentSlot) : "0",
            parentBlockhash: isSet(object.parentBlockhash) ? globalThis.String(object.parentBlockhash) : "",
            executedTransactionCount: isSet(object.executedTransactionCount)
                ? globalThis.String(object.executedTransactionCount)
                : "0",
            transactions: globalThis.Array.isArray(object?.transactions)
                ? object.transactions.map((e) => SubscribeUpdateTransactionInfo.fromJSON(e))
                : [],
            updatedAccountCount: isSet(object.updatedAccountCount) ? globalThis.String(object.updatedAccountCount) : "0",
            accounts: globalThis.Array.isArray(object?.accounts)
                ? object.accounts.map((e) => SubscribeUpdateAccountInfo.fromJSON(e))
                : [],
            entriesCount: isSet(object.entriesCount) ? globalThis.String(object.entriesCount) : "0",
            entries: globalThis.Array.isArray(object?.entries)
                ? object.entries.map((e) => SubscribeUpdateEntry.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.blockhash !== "") {
            obj.blockhash = message.blockhash;
        }
        if (message.rewards !== undefined) {
            obj.rewards = Rewards.toJSON(message.rewards);
        }
        if (message.blockTime !== undefined) {
            obj.blockTime = UnixTimestamp.toJSON(message.blockTime);
        }
        if (message.blockHeight !== undefined) {
            obj.blockHeight = BlockHeight.toJSON(message.blockHeight);
        }
        if (message.parentSlot !== "0") {
            obj.parentSlot = message.parentSlot;
        }
        if (message.parentBlockhash !== "") {
            obj.parentBlockhash = message.parentBlockhash;
        }
        if (message.executedTransactionCount !== "0") {
            obj.executedTransactionCount = message.executedTransactionCount;
        }
        if (message.transactions?.length) {
            obj.transactions = message.transactions.map((e) => SubscribeUpdateTransactionInfo.toJSON(e));
        }
        if (message.updatedAccountCount !== "0") {
            obj.updatedAccountCount = message.updatedAccountCount;
        }
        if (message.accounts?.length) {
            obj.accounts = message.accounts.map((e) => SubscribeUpdateAccountInfo.toJSON(e));
        }
        if (message.entriesCount !== "0") {
            obj.entriesCount = message.entriesCount;
        }
        if (message.entries?.length) {
            obj.entries = message.entries.map((e) => SubscribeUpdateEntry.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateBlock.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateBlock();
        message.slot = object.slot ?? "0";
        message.blockhash = object.blockhash ?? "";
        message.rewards = (object.rewards !== undefined && object.rewards !== null)
            ? Rewards.fromPartial(object.rewards)
            : undefined;
        message.blockTime = (object.blockTime !== undefined && object.blockTime !== null)
            ? UnixTimestamp.fromPartial(object.blockTime)
            : undefined;
        message.blockHeight = (object.blockHeight !== undefined && object.blockHeight !== null)
            ? BlockHeight.fromPartial(object.blockHeight)
            : undefined;
        message.parentSlot = object.parentSlot ?? "0";
        message.parentBlockhash = object.parentBlockhash ?? "";
        message.executedTransactionCount = object.executedTransactionCount ?? "0";
        message.transactions = object.transactions?.map((e) => SubscribeUpdateTransactionInfo.fromPartial(e)) || [];
        message.updatedAccountCount = object.updatedAccountCount ?? "0";
        message.accounts = object.accounts?.map((e) => SubscribeUpdateAccountInfo.fromPartial(e)) || [];
        message.entriesCount = object.entriesCount ?? "0";
        message.entries = object.entries?.map((e) => SubscribeUpdateEntry.fromPartial(e)) || [];
        return message;
    },
};
function createBaseSubscribeUpdateBlockMeta() {
    return {
        slot: "0",
        blockhash: "",
        rewards: undefined,
        blockTime: undefined,
        blockHeight: undefined,
        parentSlot: "0",
        parentBlockhash: "",
        executedTransactionCount: "0",
        entriesCount: "0",
    };
}
export const SubscribeUpdateBlockMeta = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.blockhash !== "") {
            writer.uint32(18).string(message.blockhash);
        }
        if (message.rewards !== undefined) {
            Rewards.encode(message.rewards, writer.uint32(26).fork()).ldelim();
        }
        if (message.blockTime !== undefined) {
            UnixTimestamp.encode(message.blockTime, writer.uint32(34).fork()).ldelim();
        }
        if (message.blockHeight !== undefined) {
            BlockHeight.encode(message.blockHeight, writer.uint32(42).fork()).ldelim();
        }
        if (message.parentSlot !== "0") {
            writer.uint32(48).uint64(message.parentSlot);
        }
        if (message.parentBlockhash !== "") {
            writer.uint32(58).string(message.parentBlockhash);
        }
        if (message.executedTransactionCount !== "0") {
            writer.uint32(64).uint64(message.executedTransactionCount);
        }
        if (message.entriesCount !== "0") {
            writer.uint32(72).uint64(message.entriesCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateBlockMeta();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.blockhash = reader.string();
                    continue;
                case 3:
                    if (tag !== 26) {
                        break;
                    }
                    message.rewards = Rewards.decode(reader, reader.uint32());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.blockTime = UnixTimestamp.decode(reader, reader.uint32());
                    continue;
                case 5:
                    if (tag !== 42) {
                        break;
                    }
                    message.blockHeight = BlockHeight.decode(reader, reader.uint32());
                    continue;
                case 6:
                    if (tag !== 48) {
                        break;
                    }
                    message.parentSlot = longToString(reader.uint64());
                    continue;
                case 7:
                    if (tag !== 58) {
                        break;
                    }
                    message.parentBlockhash = reader.string();
                    continue;
                case 8:
                    if (tag !== 64) {
                        break;
                    }
                    message.executedTransactionCount = longToString(reader.uint64());
                    continue;
                case 9:
                    if (tag !== 72) {
                        break;
                    }
                    message.entriesCount = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
            rewards: isSet(object.rewards) ? Rewards.fromJSON(object.rewards) : undefined,
            blockTime: isSet(object.blockTime) ? UnixTimestamp.fromJSON(object.blockTime) : undefined,
            blockHeight: isSet(object.blockHeight) ? BlockHeight.fromJSON(object.blockHeight) : undefined,
            parentSlot: isSet(object.parentSlot) ? globalThis.String(object.parentSlot) : "0",
            parentBlockhash: isSet(object.parentBlockhash) ? globalThis.String(object.parentBlockhash) : "",
            executedTransactionCount: isSet(object.executedTransactionCount)
                ? globalThis.String(object.executedTransactionCount)
                : "0",
            entriesCount: isSet(object.entriesCount) ? globalThis.String(object.entriesCount) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.blockhash !== "") {
            obj.blockhash = message.blockhash;
        }
        if (message.rewards !== undefined) {
            obj.rewards = Rewards.toJSON(message.rewards);
        }
        if (message.blockTime !== undefined) {
            obj.blockTime = UnixTimestamp.toJSON(message.blockTime);
        }
        if (message.blockHeight !== undefined) {
            obj.blockHeight = BlockHeight.toJSON(message.blockHeight);
        }
        if (message.parentSlot !== "0") {
            obj.parentSlot = message.parentSlot;
        }
        if (message.parentBlockhash !== "") {
            obj.parentBlockhash = message.parentBlockhash;
        }
        if (message.executedTransactionCount !== "0") {
            obj.executedTransactionCount = message.executedTransactionCount;
        }
        if (message.entriesCount !== "0") {
            obj.entriesCount = message.entriesCount;
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateBlockMeta.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateBlockMeta();
        message.slot = object.slot ?? "0";
        message.blockhash = object.blockhash ?? "";
        message.rewards = (object.rewards !== undefined && object.rewards !== null)
            ? Rewards.fromPartial(object.rewards)
            : undefined;
        message.blockTime = (object.blockTime !== undefined && object.blockTime !== null)
            ? UnixTimestamp.fromPartial(object.blockTime)
            : undefined;
        message.blockHeight = (object.blockHeight !== undefined && object.blockHeight !== null)
            ? BlockHeight.fromPartial(object.blockHeight)
            : undefined;
        message.parentSlot = object.parentSlot ?? "0";
        message.parentBlockhash = object.parentBlockhash ?? "";
        message.executedTransactionCount = object.executedTransactionCount ?? "0";
        message.entriesCount = object.entriesCount ?? "0";
        return message;
    },
};
function createBaseSubscribeUpdateEntry() {
    return {
        slot: "0",
        index: "0",
        numHashes: "0",
        hash: new Uint8Array(0),
        executedTransactionCount: "0",
        startingTransactionIndex: "0",
    };
}
export const SubscribeUpdateEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.index !== "0") {
            writer.uint32(16).uint64(message.index);
        }
        if (message.numHashes !== "0") {
            writer.uint32(24).uint64(message.numHashes);
        }
        if (message.hash.length !== 0) {
            writer.uint32(34).bytes(message.hash);
        }
        if (message.executedTransactionCount !== "0") {
            writer.uint32(40).uint64(message.executedTransactionCount);
        }
        if (message.startingTransactionIndex !== "0") {
            writer.uint32(48).uint64(message.startingTransactionIndex);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdateEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.index = longToString(reader.uint64());
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.numHashes = longToString(reader.uint64());
                    continue;
                case 4:
                    if (tag !== 34) {
                        break;
                    }
                    message.hash = reader.bytes();
                    continue;
                case 5:
                    if (tag !== 40) {
                        break;
                    }
                    message.executedTransactionCount = longToString(reader.uint64());
                    continue;
                case 6:
                    if (tag !== 48) {
                        break;
                    }
                    message.startingTransactionIndex = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            index: isSet(object.index) ? globalThis.String(object.index) : "0",
            numHashes: isSet(object.numHashes) ? globalThis.String(object.numHashes) : "0",
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(0),
            executedTransactionCount: isSet(object.executedTransactionCount)
                ? globalThis.String(object.executedTransactionCount)
                : "0",
            startingTransactionIndex: isSet(object.startingTransactionIndex)
                ? globalThis.String(object.startingTransactionIndex)
                : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.index !== "0") {
            obj.index = message.index;
        }
        if (message.numHashes !== "0") {
            obj.numHashes = message.numHashes;
        }
        if (message.hash.length !== 0) {
            obj.hash = base64FromBytes(message.hash);
        }
        if (message.executedTransactionCount !== "0") {
            obj.executedTransactionCount = message.executedTransactionCount;
        }
        if (message.startingTransactionIndex !== "0") {
            obj.startingTransactionIndex = message.startingTransactionIndex;
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdateEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdateEntry();
        message.slot = object.slot ?? "0";
        message.index = object.index ?? "0";
        message.numHashes = object.numHashes ?? "0";
        message.hash = object.hash ?? new Uint8Array(0);
        message.executedTransactionCount = object.executedTransactionCount ?? "0";
        message.startingTransactionIndex = object.startingTransactionIndex ?? "0";
        return message;
    },
};
function createBaseSubscribeUpdatePing() {
    return {};
}
export const SubscribeUpdatePing = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdatePing();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SubscribeUpdatePing.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSubscribeUpdatePing();
        return message;
    },
};
function createBaseSubscribeUpdatePong() {
    return { id: 0 };
}
export const SubscribeUpdatePong = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== 0) {
            writer.uint32(8).int32(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubscribeUpdatePong();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.id = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== 0) {
            obj.id = Math.round(message.id);
        }
        return obj;
    },
    create(base) {
        return SubscribeUpdatePong.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubscribeUpdatePong();
        message.id = object.id ?? 0;
        return message;
    },
};
function createBasePingRequest() {
    return { count: 0 };
}
export const PingRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.count !== 0) {
            writer.uint32(8).int32(message.count);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePingRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.count = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
    },
    toJSON(message) {
        const obj = {};
        if (message.count !== 0) {
            obj.count = Math.round(message.count);
        }
        return obj;
    },
    create(base) {
        return PingRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePingRequest();
        message.count = object.count ?? 0;
        return message;
    },
};
function createBasePongResponse() {
    return { count: 0 };
}
export const PongResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.count !== 0) {
            writer.uint32(8).int32(message.count);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePongResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.count = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
    },
    toJSON(message) {
        const obj = {};
        if (message.count !== 0) {
            obj.count = Math.round(message.count);
        }
        return obj;
    },
    create(base) {
        return PongResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePongResponse();
        message.count = object.count ?? 0;
        return message;
    },
};
function createBaseGetLatestBlockhashRequest() {
    return { commitment: undefined };
}
export const GetLatestBlockhashRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.commitment !== undefined) {
            writer.uint32(8).int32(message.commitment);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetLatestBlockhashRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.commitment = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.commitment !== undefined) {
            obj.commitment = commitmentLevelToJSON(message.commitment);
        }
        return obj;
    },
    create(base) {
        return GetLatestBlockhashRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetLatestBlockhashRequest();
        message.commitment = object.commitment ?? undefined;
        return message;
    },
};
function createBaseGetLatestBlockhashResponse() {
    return { slot: "0", blockhash: "", lastValidBlockHeight: "0" };
}
export const GetLatestBlockhashResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.blockhash !== "") {
            writer.uint32(18).string(message.blockhash);
        }
        if (message.lastValidBlockHeight !== "0") {
            writer.uint32(24).uint64(message.lastValidBlockHeight);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetLatestBlockhashResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 18) {
                        break;
                    }
                    message.blockhash = reader.string();
                    continue;
                case 3:
                    if (tag !== 24) {
                        break;
                    }
                    message.lastValidBlockHeight = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
            lastValidBlockHeight: isSet(object.lastValidBlockHeight) ? globalThis.String(object.lastValidBlockHeight) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.blockhash !== "") {
            obj.blockhash = message.blockhash;
        }
        if (message.lastValidBlockHeight !== "0") {
            obj.lastValidBlockHeight = message.lastValidBlockHeight;
        }
        return obj;
    },
    create(base) {
        return GetLatestBlockhashResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetLatestBlockhashResponse();
        message.slot = object.slot ?? "0";
        message.blockhash = object.blockhash ?? "";
        message.lastValidBlockHeight = object.lastValidBlockHeight ?? "0";
        return message;
    },
};
function createBaseGetBlockHeightRequest() {
    return { commitment: undefined };
}
export const GetBlockHeightRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.commitment !== undefined) {
            writer.uint32(8).int32(message.commitment);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.commitment = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.commitment !== undefined) {
            obj.commitment = commitmentLevelToJSON(message.commitment);
        }
        return obj;
    },
    create(base) {
        return GetBlockHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockHeightRequest();
        message.commitment = object.commitment ?? undefined;
        return message;
    },
};
function createBaseGetBlockHeightResponse() {
    return { blockHeight: "0" };
}
export const GetBlockHeightResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.blockHeight !== "0") {
            writer.uint32(8).uint64(message.blockHeight);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.blockHeight = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { blockHeight: isSet(object.blockHeight) ? globalThis.String(object.blockHeight) : "0" };
    },
    toJSON(message) {
        const obj = {};
        if (message.blockHeight !== "0") {
            obj.blockHeight = message.blockHeight;
        }
        return obj;
    },
    create(base) {
        return GetBlockHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockHeightResponse();
        message.blockHeight = object.blockHeight ?? "0";
        return message;
    },
};
function createBaseGetSlotRequest() {
    return { commitment: undefined };
}
export const GetSlotRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.commitment !== undefined) {
            writer.uint32(8).int32(message.commitment);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetSlotRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.commitment = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.commitment !== undefined) {
            obj.commitment = commitmentLevelToJSON(message.commitment);
        }
        return obj;
    },
    create(base) {
        return GetSlotRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetSlotRequest();
        message.commitment = object.commitment ?? undefined;
        return message;
    },
};
function createBaseGetSlotResponse() {
    return { slot: "0" };
}
export const GetSlotResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetSlotResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { slot: isSet(object.slot) ? globalThis.String(object.slot) : "0" };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        return obj;
    },
    create(base) {
        return GetSlotResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetSlotResponse();
        message.slot = object.slot ?? "0";
        return message;
    },
};
function createBaseGetVersionRequest() {
    return {};
}
export const GetVersionRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetVersionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetVersionRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetVersionRequest();
        return message;
    },
};
function createBaseGetVersionResponse() {
    return { version: "" };
}
export const GetVersionResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.version !== "") {
            writer.uint32(10).string(message.version);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetVersionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.version = reader.string();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { version: isSet(object.version) ? globalThis.String(object.version) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.version !== "") {
            obj.version = message.version;
        }
        return obj;
    },
    create(base) {
        return GetVersionResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetVersionResponse();
        message.version = object.version ?? "";
        return message;
    },
};
function createBaseIsBlockhashValidRequest() {
    return { blockhash: "", commitment: undefined };
}
export const IsBlockhashValidRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.blockhash !== "") {
            writer.uint32(10).string(message.blockhash);
        }
        if (message.commitment !== undefined) {
            writer.uint32(16).int32(message.commitment);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseIsBlockhashValidRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break;
                    }
                    message.blockhash = reader.string();
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.commitment = reader.int32();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
            commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.blockhash !== "") {
            obj.blockhash = message.blockhash;
        }
        if (message.commitment !== undefined) {
            obj.commitment = commitmentLevelToJSON(message.commitment);
        }
        return obj;
    },
    create(base) {
        return IsBlockhashValidRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseIsBlockhashValidRequest();
        message.blockhash = object.blockhash ?? "";
        message.commitment = object.commitment ?? undefined;
        return message;
    },
};
function createBaseIsBlockhashValidResponse() {
    return { slot: "0", valid: false };
}
export const IsBlockhashValidResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.slot !== "0") {
            writer.uint32(8).uint64(message.slot);
        }
        if (message.valid !== false) {
            writer.uint32(16).bool(message.valid);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseIsBlockhashValidResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break;
                    }
                    message.slot = longToString(reader.uint64());
                    continue;
                case 2:
                    if (tag !== 16) {
                        break;
                    }
                    message.valid = reader.bool();
                    continue;
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skipType(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            slot: isSet(object.slot) ? globalThis.String(object.slot) : "0",
            valid: isSet(object.valid) ? globalThis.Boolean(object.valid) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.slot !== "0") {
            obj.slot = message.slot;
        }
        if (message.valid !== false) {
            obj.valid = message.valid;
        }
        return obj;
    },
    create(base) {
        return IsBlockhashValidResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseIsBlockhashValidResponse();
        message.slot = object.slot ?? "0";
        message.valid = object.valid ?? false;
        return message;
    },
};
export const GeyserService = {
    subscribe: {
        path: "/geyser.Geyser/Subscribe",
        requestStream: true,
        responseStream: true,
        requestSerialize: (value) => Buffer.from(SubscribeRequest.encode(value).finish()),
        requestDeserialize: (value) => SubscribeRequest.decode(value),
        responseSerialize: (value) => Buffer.from(SubscribeUpdate.encode(value).finish()),
        responseDeserialize: (value) => SubscribeUpdate.decode(value),
    },
    ping: {
        path: "/geyser.Geyser/Ping",
        requestStream: false,
        responseStream: false,
        requestSerialize: (value) => Buffer.from(PingRequest.encode(value).finish()),
        requestDeserialize: (value) => PingRequest.decode(value),
        responseSerialize: (value) => Buffer.from(PongResponse.encode(value).finish()),
        responseDeserialize: (value) => PongResponse.decode(value),
    },
    getLatestBlockhash: {
        path: "/geyser.Geyser/GetLatestBlockhash",
        requestStream: false,
        responseStream: false,
        requestSerialize: (value) => Buffer.from(GetLatestBlockhashRequest.encode(value).finish()),
        requestDeserialize: (value) => GetLatestBlockhashRequest.decode(value),
        responseSerialize: (value) => Buffer.from(GetLatestBlockhashResponse.encode(value).finish()),
        responseDeserialize: (value) => GetLatestBlockhashResponse.decode(value),
    },
    getBlockHeight: {
        path: "/geyser.Geyser/GetBlockHeight",
        requestStream: false,
        responseStream: false,
        requestSerialize: (value) => Buffer.from(GetBlockHeightRequest.encode(value).finish()),
        requestDeserialize: (value) => GetBlockHeightRequest.decode(value),
        responseSerialize: (value) => Buffer.from(GetBlockHeightResponse.encode(value).finish()),
        responseDeserialize: (value) => GetBlockHeightResponse.decode(value),
    },
    getSlot: {
        path: "/geyser.Geyser/GetSlot",
        requestStream: false,
        responseStream: false,
        requestSerialize: (value) => Buffer.from(GetSlotRequest.encode(value).finish()),
        requestDeserialize: (value) => GetSlotRequest.decode(value),
        responseSerialize: (value) => Buffer.from(GetSlotResponse.encode(value).finish()),
        responseDeserialize: (value) => GetSlotResponse.decode(value),
    },
    isBlockhashValid: {
        path: "/geyser.Geyser/IsBlockhashValid",
        requestStream: false,
        responseStream: false,
        requestSerialize: (value) => Buffer.from(IsBlockhashValidRequest.encode(value).finish()),
        requestDeserialize: (value) => IsBlockhashValidRequest.decode(value),
        responseSerialize: (value) => Buffer.from(IsBlockhashValidResponse.encode(value).finish()),
        responseDeserialize: (value) => IsBlockhashValidResponse.decode(value),
    },
    getVersion: {
        path: "/geyser.Geyser/GetVersion",
        requestStream: false,
        responseStream: false,
        requestSerialize: (value) => Buffer.from(GetVersionRequest.encode(value).finish()),
        requestDeserialize: (value) => GetVersionRequest.decode(value),
        responseSerialize: (value) => Buffer.from(GetVersionResponse.encode(value).finish()),
        responseDeserialize: (value) => GetVersionResponse.decode(value),
    },
};
export const GeyserClient = makeGenericClientConstructor(GeyserService, "geyser.Geyser");
function bytesFromBase64(b64) {
    if (globalThis.Buffer) {
        return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = globalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (globalThis.Buffer) {
        return globalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(globalThis.String.fromCharCode(byte));
        });
        return globalThis.btoa(bin.join(""));
    }
}
function toTimestamp(date) {
    const seconds = Math.trunc(date.getTime() / 1000).toString();
    const nanos = (date.getTime() % 1000) * 1000000;
    return { seconds, nanos };
}
function fromTimestamp(t) {
    let millis = (globalThis.Number(t.seconds) || 0) * 1000;
    millis += (t.nanos || 0) / 1000000;
    return new globalThis.Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof globalThis.Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new globalThis.Date(o);
    }
    else {
        return fromTimestamp(Timestamp.fromJSON(o));
    }
}
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
