import { type TransactionError } from "@solana/rpc-types";
declare const base58: Readonly<{
    blockTime: import("@solana/rpc-types").UnixTimestamp;
    slot: bigint;
}> & Record<string, never> & {
    meta: Readonly<{
        computeUnitsConsumed?: bigint;
        err: TransactionError;
        fee: import("@solana/rpc-types").Lamports;
        logMessages: readonly string[];
        postBalances: readonly import("@solana/rpc-types").Lamports[];
        postTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        preBalances: readonly import("@solana/rpc-types").Lamports[];
        preTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        returnData?: {
            data: import("@solana/rpc-types").Base64EncodedDataResponse;
            programId: import("@solana/addresses").Address;
        };
        rewards: readonly import("@solana/rpc-types").Reward[];
        status: import("@solana/rpc-types").TransactionStatus;
    }> & Readonly<{
        innerInstructions?: readonly Readonly<{
            index: number;
            instructions: readonly Readonly<{
                accounts: readonly number[];
                data: import("@solana/rpc-types").Base58EncodedBytes;
                programIdIndex: number;
                stackHeight?: number;
            }>[];
        }>[];
    }> & Record<string, never>;
    transaction: import("@solana/rpc-types").Base58EncodedDataResponse;
};
declare const base64: Readonly<{
    blockTime: import("@solana/rpc-types").UnixTimestamp;
    slot: bigint;
}> & Record<string, never> & {
    meta: Readonly<{
        computeUnitsConsumed?: bigint;
        err: TransactionError;
        fee: import("@solana/rpc-types").Lamports;
        logMessages: readonly string[];
        postBalances: readonly import("@solana/rpc-types").Lamports[];
        postTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        preBalances: readonly import("@solana/rpc-types").Lamports[];
        preTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        returnData?: {
            data: import("@solana/rpc-types").Base64EncodedDataResponse;
            programId: import("@solana/addresses").Address;
        };
        rewards: readonly import("@solana/rpc-types").Reward[];
        status: import("@solana/rpc-types").TransactionStatus;
    }> & Readonly<{
        innerInstructions?: readonly Readonly<{
            index: number;
            instructions: readonly Readonly<{
                accounts: readonly number[];
                data: import("@solana/rpc-types").Base58EncodedBytes;
                programIdIndex: number;
                stackHeight?: number;
            }>[];
        }>[];
    }> & Record<string, never>;
    transaction: import("@solana/rpc-types").Base64EncodedDataResponse;
};
declare const json: Readonly<{
    blockTime: import("@solana/rpc-types").UnixTimestamp;
    slot: bigint;
}> & Record<string, never> & {
    meta: Readonly<{
        computeUnitsConsumed?: bigint;
        err: TransactionError;
        fee: import("@solana/rpc-types").Lamports;
        logMessages: readonly string[];
        postBalances: readonly import("@solana/rpc-types").Lamports[];
        postTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        preBalances: readonly import("@solana/rpc-types").Lamports[];
        preTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        returnData?: {
            data: import("@solana/rpc-types").Base64EncodedDataResponse;
            programId: import("@solana/addresses").Address;
        };
        rewards: readonly import("@solana/rpc-types").Reward[];
        status: import("@solana/rpc-types").TransactionStatus;
    }> & Readonly<{
        innerInstructions?: readonly Readonly<{
            index: number;
            instructions: readonly Readonly<{
                accounts: readonly number[];
                data: import("@solana/rpc-types").Base58EncodedBytes;
                programIdIndex: number;
                stackHeight?: number;
            }>[];
        }>[];
    }> & Record<string, never>;
    transaction: Readonly<{
        message: {
            accountKeys: readonly import("@solana/addresses").Address[];
            header: {
                numReadonlySignedAccounts: number;
                numReadonlyUnsignedAccounts: number;
                numRequiredSignatures: number;
            };
            instructions: readonly Readonly<{
                accounts: readonly number[];
                data: import("@solana/rpc-types").Base58EncodedBytes;
                programIdIndex: number;
                stackHeight?: number;
            }>[];
        };
    }> & Readonly<{
        message: {
            recentBlockhash: import("@solana/rpc-types").Blockhash;
        };
        signatures: readonly import("@solana/rpc-types").Base58EncodedBytes[];
    }> & Record<string, never>;
};
declare const jsonParsed: Readonly<{
    blockTime: import("@solana/rpc-types").UnixTimestamp;
    slot: bigint;
}> & Record<string, never> & {
    meta: Readonly<{
        computeUnitsConsumed?: bigint;
        err: TransactionError;
        fee: import("@solana/rpc-types").Lamports;
        logMessages: readonly string[];
        postBalances: readonly import("@solana/rpc-types").Lamports[];
        postTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        preBalances: readonly import("@solana/rpc-types").Lamports[];
        preTokenBalances?: readonly Readonly<{
            accountIndex: number;
            mint: import("@solana/addresses").Address;
            owner?: import("@solana/addresses").Address;
            programId?: import("@solana/addresses").Address;
            uiTokenAmount: Readonly<{
                amount: import("@solana/rpc-types").StringifiedBigInt;
                decimals: number;
                uiAmount: number;
                uiAmountString: import("@solana/rpc-types").StringifiedNumber;
            }>;
        }>[];
        returnData?: {
            data: import("@solana/rpc-types").Base64EncodedDataResponse;
            programId: import("@solana/addresses").Address;
        };
        rewards: readonly import("@solana/rpc-types").Reward[];
        status: import("@solana/rpc-types").TransactionStatus;
    }> & Readonly<{
        innerInstructions?: readonly Readonly<{
            index: number;
            instructions: readonly (Readonly<{
                accounts: readonly import("@solana/addresses").Address[];
                data: import("@solana/rpc-types").Base58EncodedBytes;
                programId: import("@solana/addresses").Address;
                stackHeight?: number;
            }> | Readonly<{
                parsed: {
                    info?: object;
                    type: string;
                };
                program: string;
                programId: import("@solana/addresses").Address;
                stackHeight?: number;
            }>)[];
        }>[];
    }>;
    transaction: Readonly<{
        message: {
            accountKeys: [{
                pubkey: import("@solana/addresses").Address;
                signer: boolean;
                source: string;
                writable: boolean;
            }];
            instructions: readonly (Readonly<{
                accounts: readonly import("@solana/addresses").Address[];
                data: import("@solana/rpc-types").Base58EncodedBytes;
                programId: import("@solana/addresses").Address;
                stackHeight?: number;
            }> | Readonly<{
                parsed: {
                    info?: object;
                    type: string;
                };
                program: string;
                programId: import("@solana/addresses").Address;
                stackHeight?: number;
            }>)[];
        };
    }> & Readonly<{
        message: {
            recentBlockhash: import("@solana/rpc-types").Blockhash;
        };
        signatures: readonly import("@solana/rpc-types").Base58EncodedBytes[];
    }> & Record<string, never>;
};
export type MapTransactionEncodingToReturnType = {
    0: typeof base58;
    1: typeof base64;
    2: typeof base58;
    3: typeof json;
    4: typeof jsonParsed;
};
export type TransactionErrorSolana = TransactionError;
export {};
