require('dotenv').config();
const { Connection, PublicKey, clusterApiUrl } = require('@solana/web3.js');
const Client = require('@triton-one/yellowstone-grpc').default;

class RealTimePriceMarketCap {
    constructor() {
        // Load token addresses from environment
        this.tokenMintAddress = process.env.TOKEN_MINT_ADDRESS;
        this.tokenAddress = process.env.TOKEN_ADDRESS;
        this.walletAddress = process.env.WALLET_ADDRESS;

        // gRPC and RPC setup
        this.grpcUrl = process.env.SOLANA_GRPC_URL || 'solana-yellowstone-grpc.publicnode.com:443';
        this.rpcUrl = process.env.SOLANA_RPC_URL || clusterApiUrl('mainnet-beta');

        // Initialize connections
        this.grpcClient = new Client(`https://${this.grpcUrl}`, undefined, {
            'grpc.keepalive_time_ms': 30000,
            'grpc.keepalive_timeout_ms': 5000,
            'grpc.keepalive_permit_without_calls': true,
            'grpc.http2.max_pings_without_data': 0,
            'grpc.http2.min_time_between_pings_ms': 10000,
            'grpc.http2.min_ping_interval_without_data_ms': 300000
        });

        this.rpcConnection = new Connection(this.rpcUrl, 'confirmed');

        // Cache and state management
        this.priceCache = new Map();
        this.marketCapCache = new Map();
        this.tokenSupplyCache = new Map();
        this.poolDataCache = new Map();
        this.isMonitoring = false;

        // Known DEX program IDs for price calculation
        this.dexPrograms = {
            RAYDIUM_V4: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
            RAYDIUM_V5: '5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h',
            ORCA: '9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP',
            SERUM_V3: '9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin'
        };

        this.validateInputs();

        console.log(`🔗 gRPC Connected to: ${this.grpcUrl}`);
        console.log(`🔗 RPC Connected to: ${this.rpcUrl}`);
        console.log(`🪙 Token Mint: ${this.tokenMintAddress}`);
        console.log(`📍 Token Address: ${this.tokenAddress}`);
    }

    validateInputs() {
        if (!this.tokenMintAddress) {
            throw new Error('TOKEN_MINT_ADDRESS not found in .env file');
        }
        if (!this.tokenAddress) {
            throw new Error('TOKEN_ADDRESS not found in .env file');
        }

        try {
            new PublicKey(this.tokenMintAddress);
            new PublicKey(this.tokenAddress);
        } catch (error) {
            throw new Error(`Invalid token address format: ${error.message}`);
        }
    }

    /**
     * Get token supply from on-chain data
     */
    async getTokenSupply(mintAddress) {
        if (this.tokenSupplyCache.has(mintAddress)) {
            return this.tokenSupplyCache.get(mintAddress);
        }

        try {
            const mintPublicKey = new PublicKey(mintAddress);
            const supply = await this.rpcConnection.getTokenSupply(mintPublicKey);

            const supplyData = {
                total: supply.value.amount,
                decimals: supply.value.decimals,
                totalSupply: parseFloat(supply.value.amount) / Math.pow(10, supply.value.decimals)
            };

            this.tokenSupplyCache.set(mintAddress, supplyData);
            return supplyData;

        } catch (error) {
            console.error(`❌ Error fetching token supply for ${mintAddress}:`, error.message);
            return null;
        }
    }

    /**
     * Find liquidity pools for the token
     */
    async findLiquidityPools(tokenMint) {
        try {
            const tokenMintKey = new PublicKey(tokenMint);
            const pools = [];

            // Search for Raydium pools
            const raydiumPools = await this.findRaydiumPools(tokenMintKey);
            pools.push(...raydiumPools);

            // Search for Orca pools
            const orcaPools = await this.findOrcaPools(tokenMintKey);
            pools.push(...orcaPools);

            console.log(`🏊 Found ${pools.length} liquidity pools for token ${tokenMint}`);
            return pools;

        } catch (error) {
            console.error(`❌ Error finding liquidity pools:`, error.message);
            return [];
        }
    }

    /**
     * Find Raydium liquidity pools
     */
    async findRaydiumPools(tokenMint) {
        const pools = [];

        try {
            console.log(`🔍 Searching for Raydium pools for token: ${tokenMint.toBase58()}`);

            // Search for pools where our token is either tokenA or tokenB
            const searchFilters = [
                // Token as tokenA
                {
                    memcmp: {
                        offset: 400, // Token A mint offset in Raydium pool
                        bytes: tokenMint.toBase58()
                    }
                },
                // Token as tokenB
                {
                    memcmp: {
                        offset: 432, // Token B mint offset in Raydium pool
                        bytes: tokenMint.toBase58()
                    }
                }
            ];

            for (const filter of searchFilters) {
                try {
                    const raydiumAccounts = await this.rpcConnection.getProgramAccounts(
                        new PublicKey(this.dexPrograms.RAYDIUM_V4),
                        {
                            filters: [filter]
                        }
                    );

                    console.log(`📊 Found ${raydiumAccounts.length} Raydium V4 accounts with filter`);

                    for (const account of raydiumAccounts) {
                        const poolData = await this.parseRaydiumPoolData(account);
                        if (poolData) {
                            pools.push(poolData);
                        }
                    }
                } catch (filterError) {
                    console.error(`❌ Error with Raydium filter:`, filterError.message);
                }
            }

            // Also try Raydium V5 if V4 doesn't work
            if (pools.length === 0) {
                console.log(`🔍 Trying Raydium V5 program...`);
                try {
                    const raydiumV5Accounts = await this.rpcConnection.getProgramAccounts(
                        new PublicKey(this.dexPrograms.RAYDIUM_V5)
                    );
                    console.log(`📊 Found ${raydiumV5Accounts.length} Raydium V5 accounts`);
                } catch (v5Error) {
                    console.error(`❌ Error searching Raydium V5:`, v5Error.message);
                }
            }

        } catch (error) {
            console.error(`❌ Error finding Raydium pools:`, error.message);
        }

        console.log(`🏊 Found ${pools.length} Raydium pools`);
        return pools;
    }

    /**
     * Find Orca liquidity pools
     */
    async findOrcaPools(tokenMint) {
        const pools = [];

        try {
            // Get all accounts owned by Orca program
            const orcaAccounts = await this.rpcConnection.getProgramAccounts(
                new PublicKey(this.dexPrograms.ORCA),
                {
                    filters: [
                        {
                            memcmp: {
                                offset: 101, // Token A mint offset in Orca pool
                                bytes: tokenMint.toBase58()
                            }
                        }
                    ]
                }
            );

            for (const account of orcaAccounts) {
                const poolData = await this.parseOrcaPoolData(account);
                if (poolData) {
                    pools.push(poolData);
                }
            }

        } catch (error) {
            console.error(`❌ Error finding Orca pools:`, error.message);
        }

        return pools;
    }

    /**
     * Parse Raydium pool data to extract price information
     */
    async parseRaydiumPoolData(account) {
        try {
            const data = account.account.data;
            if (data.length < 752) return null; // Minimum size for Raydium pool

            // Parse pool data (simplified - actual parsing would be more complex)
            const poolInfo = {
                address: account.pubkey.toBase58(),
                type: 'raydium',
                tokenAMint: new PublicKey(data.slice(400, 432)).toBase58(),
                tokenBMint: new PublicKey(data.slice(432, 464)).toBase58(),
                tokenAVault: new PublicKey(data.slice(464, 496)).toBase58(),
                tokenBVault: new PublicKey(data.slice(496, 528)).toBase58(),
            };

            return poolInfo;

        } catch (error) {
            console.error(`❌ Error parsing Raydium pool data:`, error.message);
            return null;
        }
    }

    /**
     * Parse Orca pool data to extract price information
     */
    async parseOrcaPoolData(account) {
        try {
            const data = account.account.data;
            if (data.length < 324) return null; // Minimum size for Orca pool

            // Parse pool data (simplified - actual parsing would be more complex)
            const poolInfo = {
                address: account.pubkey.toBase58(),
                type: 'orca',
                tokenAMint: new PublicKey(data.slice(101, 133)).toBase58(),
                tokenBMint: new PublicKey(data.slice(133, 165)).toBase58(),
                tokenAVault: new PublicKey(data.slice(165, 197)).toBase58(),
                tokenBVault: new PublicKey(data.slice(197, 229)).toBase58(),
            };

            return poolInfo;

        } catch (error) {
            console.error(`❌ Error parsing Orca pool data:`, error.message);
            return null;
        }
    }

    /**
     * Calculate token price from pool reserves
     */
    async calculateTokenPrice(tokenMint, pools) {
        let totalPrice = 0;
        let validPools = 0;

        for (const pool of pools) {
            try {
                const price = await this.calculatePoolPrice(tokenMint, pool);
                if (price > 0) {
                    totalPrice += price;
                    validPools++;
                }
            } catch (error) {
                console.error(`❌ Error calculating price for pool ${pool.address}:`, error.message);
            }
        }

        if (validPools === 0) {
            console.log(`⚠️  No valid pools found for price calculation`);
            return 0;
        }

        // Return average price across all pools
        const averagePrice = totalPrice / validPools;
        console.log(`💰 Calculated average price: $${averagePrice.toFixed(6)} from ${validPools} pools`);

        return averagePrice;
    }

    /**
     * Calculate price from individual pool
     */
    async calculatePoolPrice(tokenMint, pool) {
        try {
            // Get token balances from pool vaults
            const tokenABalance = await this.getTokenBalance(pool.tokenAVault);
            const tokenBBalance = await this.getTokenBalance(pool.tokenBVault);

            if (!tokenABalance || !tokenBBalance || tokenABalance === 0 || tokenBBalance === 0) {
                return 0;
            }

            // Determine which token is our target token and which is the quote token
            let price = 0;
            const solMint = 'So11111111111111111111111111111111111111112';
            const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

            if (pool.tokenAMint === tokenMint) {
                // Our token is tokenA
                if (pool.tokenBMint === solMint) {
                    // Price in SOL - need to convert to USD (assuming SOL = $100 for now)
                    price = (tokenBBalance / tokenABalance) * 100;
                } else if (pool.tokenBMint === usdcMint) {
                    // Price in USDC (direct USD price)
                    price = tokenBBalance / tokenABalance;
                }
            } else if (pool.tokenBMint === tokenMint) {
                // Our token is tokenB
                if (pool.tokenAMint === solMint) {
                    // Price in SOL - need to convert to USD
                    price = (tokenABalance / tokenBBalance) * 100;
                } else if (pool.tokenAMint === usdcMint) {
                    // Price in USDC (direct USD price)
                    price = tokenABalance / tokenBBalance;
                }
            }

            return price;

        } catch (error) {
            console.error(`❌ Error calculating pool price:`, error.message);
            return 0;
        }
    }

    /**
     * Get token balance from vault address
     */
    async getTokenBalance(vaultAddress) {
        try {
            const vaultPublicKey = new PublicKey(vaultAddress);
            const accountInfo = await this.rpcConnection.getAccountInfo(vaultPublicKey);

            if (!accountInfo || !accountInfo.data) {
                return 0;
            }

            // Parse token account data to get balance
            // Token account balance is stored at offset 64-72 (8 bytes, little endian)
            const balanceBuffer = Buffer.from(accountInfo.data.subarray(64, 72));
            const balance = balanceBuffer.readBigUInt64LE(0);

            return Number(balance);

        } catch (error) {
            console.error(`❌ Error getting token balance for ${vaultAddress}:`, error.message);
            return 0;
        }
    }

    /**
     * Calculate market cap
     */
    async calculateMarketCap(tokenMint, price) {
        try {
            const supplyData = await this.getTokenSupply(tokenMint);
            if (!supplyData) {
                console.log(`⚠️  Could not get token supply for market cap calculation`);
                return 0;
            }

            const marketCap = supplyData.totalSupply * price;
            console.log(`📊 Market Cap: $${marketCap.toLocaleString()} (Supply: ${supplyData.totalSupply.toLocaleString()} × Price: $${price.toFixed(6)})`);

            return marketCap;

        } catch (error) {
            console.error(`❌ Error calculating market cap:`, error.message);
            return 0;
        }
    }

    /**
     * Start real-time monitoring using gRPC Yellowstone
     */
    async startRealTimeMonitoring() {
        if (this.isMonitoring) {
            console.log(`⚠️  Real-time monitoring is already active`);
            return;
        }

        console.log(`🚀 Starting real-time price and market cap monitoring...`);

        try {
            // First, get initial price and market cap
            await this.updatePriceAndMarketCap();

            // Find liquidity pools to monitor
            const pools = await this.findLiquidityPools(this.tokenMintAddress);
            if (pools.length === 0) {
                console.log(`❌ No liquidity pools found for monitoring`);
                return;
            }

            // Set up gRPC subscription for pool account updates
            const stream = await this.grpcClient.subscribe();
            this.isMonitoring = true;

            // Subscribe to pool account updates
            const subscribeRequest = {
                accounts: {},
                slots: {},
                transactions: {},
                blocks: {},
                blocksMeta: {},
                accountsDataSlice: []
            };

            // Add each pool to monitoring
            pools.forEach((pool, index) => {
                subscribeRequest.accounts[`pool_${index}`] = {
                    account: [pool.tokenAVault, pool.tokenBVault],
                    owner: [],
                    filters: []
                };
            });

            console.log(`📡 Subscribing to ${pools.length} liquidity pools for real-time updates...`);

            // Send subscription request
            stream.write(subscribeRequest);

            // Handle incoming updates
            stream.on('data', async (data) => {
                await this.handleAccountUpdate(data);
            });

            stream.on('error', (error) => {
                console.error('❌ gRPC stream error:', error);
                this.isMonitoring = false;
            });

            stream.on('end', () => {
                console.log('📡 gRPC stream ended');
                this.isMonitoring = false;
            });

            // Keep connection alive with periodic pings
            this.startPingInterval(stream);

            console.log(`✅ Real-time monitoring started successfully`);

        } catch (error) {
            console.error('❌ Failed to start real-time monitoring:', error);
            this.isMonitoring = false;
        }
    }

    /**
     * Handle account updates from gRPC stream
     */
    async handleAccountUpdate(data) {
        try {
            if (data.account) {
                console.log(`🔄 Pool account updated, recalculating price...`);

                // Debounce updates to avoid excessive calculations
                clearTimeout(this.updateTimeout);
                this.updateTimeout = setTimeout(async () => {
                    await this.updatePriceAndMarketCap();
                }, 1000); // Wait 1 second before updating
            }
        } catch (error) {
            console.error('❌ Error handling account update:', error);
        }
    }

    /**
     * Update price and market cap
     */
    async updatePriceAndMarketCap() {
        try {
            console.log(`🔄 Updating price and market cap...`);

            // Find current liquidity pools
            const pools = await this.findLiquidityPools(this.tokenMintAddress);

            if (pools.length === 0) {
                console.log(`⚠️  No liquidity pools found for price calculation`);
                return;
            }

            // Calculate current price
            const price = await this.calculateTokenPrice(this.tokenMintAddress, pools);

            if (price > 0) {
                // Calculate market cap
                const marketCap = await this.calculateMarketCap(this.tokenMintAddress, price);

                // Update cache
                this.priceCache.set(this.tokenMintAddress, {
                    price: price,
                    timestamp: Date.now()
                });

                this.marketCapCache.set(this.tokenMintAddress, {
                    marketCap: marketCap,
                    timestamp: Date.now()
                });

                // Display results
                this.displayCurrentData(price, marketCap);
            }

        } catch (error) {
            console.error('❌ Error updating price and market cap:', error);
        }
    }

    /**
     * Display current price and market cap data
     */
    displayCurrentData(price, marketCap) {
        const timestamp = new Date().toLocaleTimeString();

        console.log(`\n📊 ===== REAL-TIME DATA UPDATE =====`);
        console.log(`⏰ Time: ${timestamp}`);
        console.log(`🪙 Token: ${this.tokenMintAddress}`);
        console.log(`💰 Price: $${price.toFixed(6)}`);
        console.log(`📈 Market Cap: $${marketCap.toLocaleString()}`);
        console.log(`=====================================\n`);
    }

    /**
     * Start ping interval to keep gRPC connection alive
     */
    startPingInterval(stream) {
        const PING_INTERVAL = 30000; // 30 seconds

        setInterval(async () => {
            if (!this.isMonitoring) return;

            const pingRequest = {
                ping: { id: Date.now() },
                slots: {},
                accounts: {},
                transactions: {},
                blocks: {},
                blocksMeta: {},
                accountsDataSlice: []
            };

            try {
                stream.write(pingRequest);
            } catch (error) {
                console.error('❌ Ping failed:', error);
            }
        }, PING_INTERVAL);
    }

    /**
     * Get current cached price
     */
    getCurrentPrice(tokenMint = this.tokenMintAddress) {
        const cached = this.priceCache.get(tokenMint);
        if (cached && (Date.now() - cached.timestamp) < 60000) { // 1 minute cache
            return cached.price;
        }
        return null;
    }

    /**
     * Get current cached market cap
     */
    getCurrentMarketCap(tokenMint = this.tokenMintAddress) {
        const cached = this.marketCapCache.get(tokenMint);
        if (cached && (Date.now() - cached.timestamp) < 60000) { // 1 minute cache
            return cached.marketCap;
        }
        return null;
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        this.isMonitoring = false;
        console.log(`🛑 Real-time monitoring stopped`);
    }
}

// Main execution function
async function main() {
    try {
        console.log(`🚀 Starting Real-Time Price & Market Cap Monitor`);
        console.log(`================================================`);

        const monitor = new RealTimePriceMarketCap();

        // Start real-time monitoring
        await monitor.startRealTimeMonitoring();

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log(`\n🛑 Shutting down gracefully...`);
            monitor.stopMonitoring();
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            console.log(`\n🛑 Shutting down gracefully...`);
            monitor.stopMonitoring();
            process.exit(0);
        });

        // Keep the process running
        console.log(`✅ Monitor is running. Press Ctrl+C to stop.`);

    } catch (error) {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    }
}

// Export the class for use in other modules
module.exports = RealTimePriceMarketCap;

// Run main function if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}