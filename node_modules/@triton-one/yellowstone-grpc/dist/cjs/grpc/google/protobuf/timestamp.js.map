{"version": 3, "file": "timestamp.js", "sourceRoot": "", "sources": ["../../../../../src/grpc/google/protobuf/timestamp.ts"], "names": [], "mappings": ";;;;;;AAOA,8CAAwB;AACxB,+DAAqC;AAExB,QAAA,eAAe,GAAG,iBAAiB,CAAC;AA6GjD,SAAS,mBAAmB;IAC1B,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACpC,CAAC;AAEY,QAAA,SAAS,GAAG;IACvB,MAAM,YAAC,OAAkB,EAAE,MAAwC;QAAxC,uBAAA,EAAA,SAAqB,iBAAG,CAAC,MAAM,CAAC,MAAM,EAAE;QACjE,IAAI,OAAO,CAAC,OAAO,KAAK,GAAG,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACxC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,YAAC,KAA8B,EAAE,MAAe;QACpD,IAAM,MAAM,GAAG,KAAK,YAAY,iBAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAG,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,IAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC;oBACJ,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,EAAU,CAAC,CAAC;oBACvD,SAAS;gBACX,KAAK,CAAC;oBACJ,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC/B,SAAS;aACZ;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SAC1B;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,YAAC,MAAW;QAClB,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG;YACxE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACjE,CAAC;IACJ,CAAC;IAED,MAAM,YAAC,OAAkB;QACvB,IAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,OAAO,KAAK,GAAG,EAAE;YAC3B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC/B;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,YAA6C,IAAQ;QACzD,OAAO,iBAAS,CAAC,WAAW,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAK,EAAU,CAAC,CAAC;IACpD,CAAC;IACD,WAAW,YAA6C,MAAS;;QAC/D,IAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,CAAC,OAAO,GAAG,MAAA,MAAM,CAAC,OAAO,mCAAI,GAAG,CAAC;QACxC,OAAO,CAAC,KAAK,GAAG,MAAA,MAAM,CAAC,KAAK,mCAAI,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAcF,SAAS,YAAY,CAAC,IAAU;IAC9B,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AACzB,CAAC;AAED,IAAI,iBAAG,CAAC,IAAI,CAAC,IAAI,KAAK,cAAI,EAAE;IAC1B,iBAAG,CAAC,IAAI,CAAC,IAAI,GAAG,cAAW,CAAC;IAC5B,iBAAG,CAAC,SAAS,EAAE,CAAC;CACjB;AAED,SAAS,KAAK,CAAC,KAAU;IACvB,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC"}