import _m0 from "protobufjs/minimal";
export declare const protobufPackage = "solana.storage.ConfirmedBlock";
export declare enum RewardType {
    Unspecified = 0,
    Fee = 1,
    Rent = 2,
    Staking = 3,
    Voting = 4,
    UNRECOGNIZED = -1
}
export declare function rewardTypeFromJSON(object: any): RewardType;
export declare function rewardTypeToJSON(object: RewardType): string;
export interface ConfirmedBlock {
    previousBlockhash: string;
    blockhash: string;
    parentSlot: string;
    transactions: ConfirmedTransaction[];
    rewards: Reward[];
    blockTime: UnixTimestamp | undefined;
    blockHeight: BlockHeight | undefined;
    numPartitions: NumPartitions | undefined;
}
export interface ConfirmedTransaction {
    transaction: Transaction | undefined;
    meta: TransactionStatusMeta | undefined;
}
export interface Transaction {
    signatures: Uint8Array[];
    message: Message | undefined;
}
export interface Message {
    header: MessageHeader | undefined;
    accountKeys: Uint8Array[];
    recentBlockhash: Uint8Array;
    instructions: CompiledInstruction[];
    versioned: boolean;
    addressTableLookups: MessageAddressTableLookup[];
}
export interface MessageHeader {
    numRequiredSignatures: number;
    numReadonlySignedAccounts: number;
    numReadonlyUnsignedAccounts: number;
}
export interface MessageAddressTableLookup {
    accountKey: Uint8Array;
    writableIndexes: Uint8Array;
    readonlyIndexes: Uint8Array;
}
export interface TransactionStatusMeta {
    err: TransactionError | undefined;
    fee: string;
    preBalances: string[];
    postBalances: string[];
    innerInstructions: InnerInstructions[];
    innerInstructionsNone: boolean;
    logMessages: string[];
    logMessagesNone: boolean;
    preTokenBalances: TokenBalance[];
    postTokenBalances: TokenBalance[];
    rewards: Reward[];
    loadedWritableAddresses: Uint8Array[];
    loadedReadonlyAddresses: Uint8Array[];
    returnData: ReturnData | undefined;
    returnDataNone: boolean;
    /**
     * Sum of compute units consumed by all instructions.
     * Available since Solana v1.10.35 / v1.11.6.
     * Set to `None` for txs executed on earlier versions.
     */
    computeUnitsConsumed?: string | undefined;
}
export interface TransactionError {
    err: Uint8Array;
}
export interface InnerInstructions {
    index: number;
    instructions: InnerInstruction[];
}
export interface InnerInstruction {
    programIdIndex: number;
    accounts: Uint8Array;
    data: Uint8Array;
    /**
     * Invocation stack height of an inner instruction.
     * Available since Solana v1.14.6
     * Set to `None` for txs executed on earlier versions.
     */
    stackHeight?: number | undefined;
}
export interface CompiledInstruction {
    programIdIndex: number;
    accounts: Uint8Array;
    data: Uint8Array;
}
export interface TokenBalance {
    accountIndex: number;
    mint: string;
    uiTokenAmount: UiTokenAmount | undefined;
    owner: string;
    programId: string;
}
export interface UiTokenAmount {
    uiAmount: number;
    decimals: number;
    amount: string;
    uiAmountString: string;
}
export interface ReturnData {
    programId: Uint8Array;
    data: Uint8Array;
}
export interface Reward {
    pubkey: string;
    lamports: string;
    postBalance: string;
    rewardType: RewardType;
    commission: string;
}
export interface Rewards {
    rewards: Reward[];
    numPartitions: NumPartitions | undefined;
}
export interface UnixTimestamp {
    timestamp: string;
}
export interface BlockHeight {
    blockHeight: string;
}
export interface NumPartitions {
    numPartitions: string;
}
export declare const ConfirmedBlock: {
    encode(message: ConfirmedBlock, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConfirmedBlock;
    fromJSON(object: any): ConfirmedBlock;
    toJSON(message: ConfirmedBlock): unknown;
    create<I extends {
        previousBlockhash?: string;
        blockhash?: string;
        parentSlot?: string;
        transactions?: {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        }[];
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[];
        blockTime?: {
            timestamp?: string;
        };
        blockHeight?: {
            blockHeight?: string;
        };
        numPartitions?: {
            numPartitions?: string;
        };
    } & {
        previousBlockhash?: string;
        blockhash?: string;
        parentSlot?: string;
        transactions?: {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        }[] & ({
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        } & {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            } & {
                signatures?: Uint8Array[] & Uint8Array[] & { [K in Exclude<keyof I["transactions"][number]["transaction"]["signatures"], keyof Uint8Array[]>]: never; };
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                } & {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    } & {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    } & { [K_1 in Exclude<keyof I["transactions"][number]["transaction"]["message"]["header"], keyof MessageHeader>]: never; };
                    accountKeys?: Uint8Array[] & Uint8Array[] & { [K_2 in Exclude<keyof I["transactions"][number]["transaction"]["message"]["accountKeys"], keyof Uint8Array[]>]: never; };
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[] & ({
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    } & {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    } & { [K_3 in Exclude<keyof I["transactions"][number]["transaction"]["message"]["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_4 in Exclude<keyof I["transactions"][number]["transaction"]["message"]["instructions"], keyof {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[]>]: never; };
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[] & ({
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    } & {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    } & { [K_5 in Exclude<keyof I["transactions"][number]["transaction"]["message"]["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_6 in Exclude<keyof I["transactions"][number]["transaction"]["message"]["addressTableLookups"], keyof {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[]>]: never; };
                } & { [K_7 in Exclude<keyof I["transactions"][number]["transaction"]["message"], keyof Message>]: never; };
            } & { [K_8 in Exclude<keyof I["transactions"][number]["transaction"], keyof Transaction>]: never; };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            } & {
                err?: {
                    err?: Uint8Array;
                } & {
                    err?: Uint8Array;
                } & { [K_9 in Exclude<keyof I["transactions"][number]["meta"]["err"], "err">]: never; };
                fee?: string;
                preBalances?: string[] & string[] & { [K_10 in Exclude<keyof I["transactions"][number]["meta"]["preBalances"], keyof string[]>]: never; };
                postBalances?: string[] & string[] & { [K_11 in Exclude<keyof I["transactions"][number]["meta"]["postBalances"], keyof string[]>]: never; };
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[] & ({
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                } & {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[] & ({
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    } & {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    } & { [K_12 in Exclude<keyof I["transactions"][number]["meta"]["innerInstructions"][number]["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_13 in Exclude<keyof I["transactions"][number]["meta"]["innerInstructions"][number]["instructions"], keyof {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[]>]: never; };
                } & { [K_14 in Exclude<keyof I["transactions"][number]["meta"]["innerInstructions"][number], keyof InnerInstructions>]: never; })[] & { [K_15 in Exclude<keyof I["transactions"][number]["meta"]["innerInstructions"], keyof {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[]>]: never; };
                innerInstructionsNone?: boolean;
                logMessages?: string[] & string[] & { [K_16 in Exclude<keyof I["transactions"][number]["meta"]["logMessages"], keyof string[]>]: never; };
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[] & ({
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                } & {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & { [K_17 in Exclude<keyof I["transactions"][number]["meta"]["preTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                    owner?: string;
                    programId?: string;
                } & { [K_18 in Exclude<keyof I["transactions"][number]["meta"]["preTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_19 in Exclude<keyof I["transactions"][number]["meta"]["preTokenBalances"], keyof {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[]>]: never; };
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[] & ({
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                } & {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & { [K_20 in Exclude<keyof I["transactions"][number]["meta"]["postTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                    owner?: string;
                    programId?: string;
                } & { [K_21 in Exclude<keyof I["transactions"][number]["meta"]["postTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_22 in Exclude<keyof I["transactions"][number]["meta"]["postTokenBalances"], keyof {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[]>]: never; };
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[] & ({
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                } & {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                } & { [K_23 in Exclude<keyof I["transactions"][number]["meta"]["rewards"][number], keyof Reward>]: never; })[] & { [K_24 in Exclude<keyof I["transactions"][number]["meta"]["rewards"], keyof {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[]>]: never; };
                loadedWritableAddresses?: Uint8Array[] & Uint8Array[] & { [K_25 in Exclude<keyof I["transactions"][number]["meta"]["loadedWritableAddresses"], keyof Uint8Array[]>]: never; };
                loadedReadonlyAddresses?: Uint8Array[] & Uint8Array[] & { [K_26 in Exclude<keyof I["transactions"][number]["meta"]["loadedReadonlyAddresses"], keyof Uint8Array[]>]: never; };
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                } & {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                } & { [K_27 in Exclude<keyof I["transactions"][number]["meta"]["returnData"], keyof ReturnData>]: never; };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            } & { [K_28 in Exclude<keyof I["transactions"][number]["meta"], keyof TransactionStatusMeta>]: never; };
        } & { [K_29 in Exclude<keyof I["transactions"][number], keyof ConfirmedTransaction>]: never; })[] & { [K_30 in Exclude<keyof I["transactions"], keyof {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        }[]>]: never; };
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[] & ({
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & { [K_31 in Exclude<keyof I["rewards"][number], keyof Reward>]: never; })[] & { [K_32 in Exclude<keyof I["rewards"], keyof {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[]>]: never; };
        blockTime?: {
            timestamp?: string;
        } & {
            timestamp?: string;
        } & { [K_33 in Exclude<keyof I["blockTime"], "timestamp">]: never; };
        blockHeight?: {
            blockHeight?: string;
        } & {
            blockHeight?: string;
        } & { [K_34 in Exclude<keyof I["blockHeight"], "blockHeight">]: never; };
        numPartitions?: {
            numPartitions?: string;
        } & {
            numPartitions?: string;
        } & { [K_35 in Exclude<keyof I["numPartitions"], "numPartitions">]: never; };
    } & { [K_36 in Exclude<keyof I, keyof ConfirmedBlock>]: never; }>(base?: I): ConfirmedBlock;
    fromPartial<I_1 extends {
        previousBlockhash?: string;
        blockhash?: string;
        parentSlot?: string;
        transactions?: {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        }[];
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[];
        blockTime?: {
            timestamp?: string;
        };
        blockHeight?: {
            blockHeight?: string;
        };
        numPartitions?: {
            numPartitions?: string;
        };
    } & {
        previousBlockhash?: string;
        blockhash?: string;
        parentSlot?: string;
        transactions?: {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        }[] & ({
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        } & {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            } & {
                signatures?: Uint8Array[] & Uint8Array[] & { [K_37 in Exclude<keyof I_1["transactions"][number]["transaction"]["signatures"], keyof Uint8Array[]>]: never; };
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                } & {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    } & {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    } & { [K_38 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"]["header"], keyof MessageHeader>]: never; };
                    accountKeys?: Uint8Array[] & Uint8Array[] & { [K_39 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"]["accountKeys"], keyof Uint8Array[]>]: never; };
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[] & ({
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    } & {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    } & { [K_40 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"]["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_41 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"]["instructions"], keyof {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[]>]: never; };
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[] & ({
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    } & {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    } & { [K_42 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"]["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_43 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"]["addressTableLookups"], keyof {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[]>]: never; };
                } & { [K_44 in Exclude<keyof I_1["transactions"][number]["transaction"]["message"], keyof Message>]: never; };
            } & { [K_45 in Exclude<keyof I_1["transactions"][number]["transaction"], keyof Transaction>]: never; };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            } & {
                err?: {
                    err?: Uint8Array;
                } & {
                    err?: Uint8Array;
                } & { [K_46 in Exclude<keyof I_1["transactions"][number]["meta"]["err"], "err">]: never; };
                fee?: string;
                preBalances?: string[] & string[] & { [K_47 in Exclude<keyof I_1["transactions"][number]["meta"]["preBalances"], keyof string[]>]: never; };
                postBalances?: string[] & string[] & { [K_48 in Exclude<keyof I_1["transactions"][number]["meta"]["postBalances"], keyof string[]>]: never; };
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[] & ({
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                } & {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[] & ({
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    } & {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    } & { [K_49 in Exclude<keyof I_1["transactions"][number]["meta"]["innerInstructions"][number]["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_50 in Exclude<keyof I_1["transactions"][number]["meta"]["innerInstructions"][number]["instructions"], keyof {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[]>]: never; };
                } & { [K_51 in Exclude<keyof I_1["transactions"][number]["meta"]["innerInstructions"][number], keyof InnerInstructions>]: never; })[] & { [K_52 in Exclude<keyof I_1["transactions"][number]["meta"]["innerInstructions"], keyof {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[]>]: never; };
                innerInstructionsNone?: boolean;
                logMessages?: string[] & string[] & { [K_53 in Exclude<keyof I_1["transactions"][number]["meta"]["logMessages"], keyof string[]>]: never; };
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[] & ({
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                } & {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & { [K_54 in Exclude<keyof I_1["transactions"][number]["meta"]["preTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                    owner?: string;
                    programId?: string;
                } & { [K_55 in Exclude<keyof I_1["transactions"][number]["meta"]["preTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_56 in Exclude<keyof I_1["transactions"][number]["meta"]["preTokenBalances"], keyof {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[]>]: never; };
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[] & ({
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                } & {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    } & { [K_57 in Exclude<keyof I_1["transactions"][number]["meta"]["postTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                    owner?: string;
                    programId?: string;
                } & { [K_58 in Exclude<keyof I_1["transactions"][number]["meta"]["postTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_59 in Exclude<keyof I_1["transactions"][number]["meta"]["postTokenBalances"], keyof {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[]>]: never; };
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[] & ({
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                } & {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                } & { [K_60 in Exclude<keyof I_1["transactions"][number]["meta"]["rewards"][number], keyof Reward>]: never; })[] & { [K_61 in Exclude<keyof I_1["transactions"][number]["meta"]["rewards"], keyof {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[]>]: never; };
                loadedWritableAddresses?: Uint8Array[] & Uint8Array[] & { [K_62 in Exclude<keyof I_1["transactions"][number]["meta"]["loadedWritableAddresses"], keyof Uint8Array[]>]: never; };
                loadedReadonlyAddresses?: Uint8Array[] & Uint8Array[] & { [K_63 in Exclude<keyof I_1["transactions"][number]["meta"]["loadedReadonlyAddresses"], keyof Uint8Array[]>]: never; };
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                } & {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                } & { [K_64 in Exclude<keyof I_1["transactions"][number]["meta"]["returnData"], keyof ReturnData>]: never; };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            } & { [K_65 in Exclude<keyof I_1["transactions"][number]["meta"], keyof TransactionStatusMeta>]: never; };
        } & { [K_66 in Exclude<keyof I_1["transactions"][number], keyof ConfirmedTransaction>]: never; })[] & { [K_67 in Exclude<keyof I_1["transactions"], keyof {
            transaction?: {
                signatures?: Uint8Array[];
                message?: {
                    header?: {
                        numRequiredSignatures?: number;
                        numReadonlySignedAccounts?: number;
                        numReadonlyUnsignedAccounts?: number;
                    };
                    accountKeys?: Uint8Array[];
                    recentBlockhash?: Uint8Array;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                    }[];
                    versioned?: boolean;
                    addressTableLookups?: {
                        accountKey?: Uint8Array;
                        writableIndexes?: Uint8Array;
                        readonlyIndexes?: Uint8Array;
                    }[];
                };
            };
            meta?: {
                err?: {
                    err?: Uint8Array;
                };
                fee?: string;
                preBalances?: string[];
                postBalances?: string[];
                innerInstructions?: {
                    index?: number;
                    instructions?: {
                        programIdIndex?: number;
                        accounts?: Uint8Array;
                        data?: Uint8Array;
                        stackHeight?: number | undefined;
                    }[];
                }[];
                innerInstructionsNone?: boolean;
                logMessages?: string[];
                logMessagesNone?: boolean;
                preTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                postTokenBalances?: {
                    accountIndex?: number;
                    mint?: string;
                    uiTokenAmount?: {
                        uiAmount?: number;
                        decimals?: number;
                        amount?: string;
                        uiAmountString?: string;
                    };
                    owner?: string;
                    programId?: string;
                }[];
                rewards?: {
                    pubkey?: string;
                    lamports?: string;
                    postBalance?: string;
                    rewardType?: RewardType;
                    commission?: string;
                }[];
                loadedWritableAddresses?: Uint8Array[];
                loadedReadonlyAddresses?: Uint8Array[];
                returnData?: {
                    programId?: Uint8Array;
                    data?: Uint8Array;
                };
                returnDataNone?: boolean;
                computeUnitsConsumed?: string | undefined;
            };
        }[]>]: never; };
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[] & ({
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & { [K_68 in Exclude<keyof I_1["rewards"][number], keyof Reward>]: never; })[] & { [K_69 in Exclude<keyof I_1["rewards"], keyof {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[]>]: never; };
        blockTime?: {
            timestamp?: string;
        } & {
            timestamp?: string;
        } & { [K_70 in Exclude<keyof I_1["blockTime"], "timestamp">]: never; };
        blockHeight?: {
            blockHeight?: string;
        } & {
            blockHeight?: string;
        } & { [K_71 in Exclude<keyof I_1["blockHeight"], "blockHeight">]: never; };
        numPartitions?: {
            numPartitions?: string;
        } & {
            numPartitions?: string;
        } & { [K_72 in Exclude<keyof I_1["numPartitions"], "numPartitions">]: never; };
    } & { [K_73 in Exclude<keyof I_1, keyof ConfirmedBlock>]: never; }>(object: I_1): ConfirmedBlock;
};
export declare const ConfirmedTransaction: {
    encode(message: ConfirmedTransaction, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConfirmedTransaction;
    fromJSON(object: any): ConfirmedTransaction;
    toJSON(message: ConfirmedTransaction): unknown;
    create<I extends {
        transaction?: {
            signatures?: Uint8Array[];
            message?: {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                };
                accountKeys?: Uint8Array[];
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[];
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[];
            };
        };
        meta?: {
            err?: {
                err?: Uint8Array;
            };
            fee?: string;
            preBalances?: string[];
            postBalances?: string[];
            innerInstructions?: {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[];
            innerInstructionsNone?: boolean;
            logMessages?: string[];
            logMessagesNone?: boolean;
            preTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            postTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            rewards?: {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[];
            loadedWritableAddresses?: Uint8Array[];
            loadedReadonlyAddresses?: Uint8Array[];
            returnData?: {
                programId?: Uint8Array;
                data?: Uint8Array;
            };
            returnDataNone?: boolean;
            computeUnitsConsumed?: string | undefined;
        };
    } & {
        transaction?: {
            signatures?: Uint8Array[];
            message?: {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                };
                accountKeys?: Uint8Array[];
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[];
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[];
            };
        } & {
            signatures?: Uint8Array[] & Uint8Array[] & { [K in Exclude<keyof I["transaction"]["signatures"], keyof Uint8Array[]>]: never; };
            message?: {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                };
                accountKeys?: Uint8Array[];
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[];
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[];
            } & {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                } & {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                } & { [K_1 in Exclude<keyof I["transaction"]["message"]["header"], keyof MessageHeader>]: never; };
                accountKeys?: Uint8Array[] & Uint8Array[] & { [K_2 in Exclude<keyof I["transaction"]["message"]["accountKeys"], keyof Uint8Array[]>]: never; };
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[] & ({
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                } & {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                } & { [K_3 in Exclude<keyof I["transaction"]["message"]["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_4 in Exclude<keyof I["transaction"]["message"]["instructions"], keyof {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[]>]: never; };
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[] & ({
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                } & {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                } & { [K_5 in Exclude<keyof I["transaction"]["message"]["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_6 in Exclude<keyof I["transaction"]["message"]["addressTableLookups"], keyof {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[]>]: never; };
            } & { [K_7 in Exclude<keyof I["transaction"]["message"], keyof Message>]: never; };
        } & { [K_8 in Exclude<keyof I["transaction"], keyof Transaction>]: never; };
        meta?: {
            err?: {
                err?: Uint8Array;
            };
            fee?: string;
            preBalances?: string[];
            postBalances?: string[];
            innerInstructions?: {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[];
            innerInstructionsNone?: boolean;
            logMessages?: string[];
            logMessagesNone?: boolean;
            preTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            postTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            rewards?: {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[];
            loadedWritableAddresses?: Uint8Array[];
            loadedReadonlyAddresses?: Uint8Array[];
            returnData?: {
                programId?: Uint8Array;
                data?: Uint8Array;
            };
            returnDataNone?: boolean;
            computeUnitsConsumed?: string | undefined;
        } & {
            err?: {
                err?: Uint8Array;
            } & {
                err?: Uint8Array;
            } & { [K_9 in Exclude<keyof I["meta"]["err"], "err">]: never; };
            fee?: string;
            preBalances?: string[] & string[] & { [K_10 in Exclude<keyof I["meta"]["preBalances"], keyof string[]>]: never; };
            postBalances?: string[] & string[] & { [K_11 in Exclude<keyof I["meta"]["postBalances"], keyof string[]>]: never; };
            innerInstructions?: {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[] & ({
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            } & {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[] & ({
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                } & {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                } & { [K_12 in Exclude<keyof I["meta"]["innerInstructions"][number]["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_13 in Exclude<keyof I["meta"]["innerInstructions"][number]["instructions"], keyof {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[]>]: never; };
            } & { [K_14 in Exclude<keyof I["meta"]["innerInstructions"][number], keyof InnerInstructions>]: never; })[] & { [K_15 in Exclude<keyof I["meta"]["innerInstructions"], keyof {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[]>]: never; };
            innerInstructionsNone?: boolean;
            logMessages?: string[] & string[] & { [K_16 in Exclude<keyof I["meta"]["logMessages"], keyof string[]>]: never; };
            logMessagesNone?: boolean;
            preTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[] & ({
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            } & {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & { [K_17 in Exclude<keyof I["meta"]["preTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                owner?: string;
                programId?: string;
            } & { [K_18 in Exclude<keyof I["meta"]["preTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_19 in Exclude<keyof I["meta"]["preTokenBalances"], keyof {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[]>]: never; };
            postTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[] & ({
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            } & {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & { [K_20 in Exclude<keyof I["meta"]["postTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                owner?: string;
                programId?: string;
            } & { [K_21 in Exclude<keyof I["meta"]["postTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_22 in Exclude<keyof I["meta"]["postTokenBalances"], keyof {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[]>]: never; };
            rewards?: {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[] & ({
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            } & {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            } & { [K_23 in Exclude<keyof I["meta"]["rewards"][number], keyof Reward>]: never; })[] & { [K_24 in Exclude<keyof I["meta"]["rewards"], keyof {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[]>]: never; };
            loadedWritableAddresses?: Uint8Array[] & Uint8Array[] & { [K_25 in Exclude<keyof I["meta"]["loadedWritableAddresses"], keyof Uint8Array[]>]: never; };
            loadedReadonlyAddresses?: Uint8Array[] & Uint8Array[] & { [K_26 in Exclude<keyof I["meta"]["loadedReadonlyAddresses"], keyof Uint8Array[]>]: never; };
            returnData?: {
                programId?: Uint8Array;
                data?: Uint8Array;
            } & {
                programId?: Uint8Array;
                data?: Uint8Array;
            } & { [K_27 in Exclude<keyof I["meta"]["returnData"], keyof ReturnData>]: never; };
            returnDataNone?: boolean;
            computeUnitsConsumed?: string | undefined;
        } & { [K_28 in Exclude<keyof I["meta"], keyof TransactionStatusMeta>]: never; };
    } & { [K_29 in Exclude<keyof I, keyof ConfirmedTransaction>]: never; }>(base?: I): ConfirmedTransaction;
    fromPartial<I_1 extends {
        transaction?: {
            signatures?: Uint8Array[];
            message?: {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                };
                accountKeys?: Uint8Array[];
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[];
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[];
            };
        };
        meta?: {
            err?: {
                err?: Uint8Array;
            };
            fee?: string;
            preBalances?: string[];
            postBalances?: string[];
            innerInstructions?: {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[];
            innerInstructionsNone?: boolean;
            logMessages?: string[];
            logMessagesNone?: boolean;
            preTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            postTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            rewards?: {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[];
            loadedWritableAddresses?: Uint8Array[];
            loadedReadonlyAddresses?: Uint8Array[];
            returnData?: {
                programId?: Uint8Array;
                data?: Uint8Array;
            };
            returnDataNone?: boolean;
            computeUnitsConsumed?: string | undefined;
        };
    } & {
        transaction?: {
            signatures?: Uint8Array[];
            message?: {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                };
                accountKeys?: Uint8Array[];
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[];
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[];
            };
        } & {
            signatures?: Uint8Array[] & Uint8Array[] & { [K_30 in Exclude<keyof I_1["transaction"]["signatures"], keyof Uint8Array[]>]: never; };
            message?: {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                };
                accountKeys?: Uint8Array[];
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[];
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[];
            } & {
                header?: {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                } & {
                    numRequiredSignatures?: number;
                    numReadonlySignedAccounts?: number;
                    numReadonlyUnsignedAccounts?: number;
                } & { [K_31 in Exclude<keyof I_1["transaction"]["message"]["header"], keyof MessageHeader>]: never; };
                accountKeys?: Uint8Array[] & Uint8Array[] & { [K_32 in Exclude<keyof I_1["transaction"]["message"]["accountKeys"], keyof Uint8Array[]>]: never; };
                recentBlockhash?: Uint8Array;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[] & ({
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                } & {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                } & { [K_33 in Exclude<keyof I_1["transaction"]["message"]["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_34 in Exclude<keyof I_1["transaction"]["message"]["instructions"], keyof {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                }[]>]: never; };
                versioned?: boolean;
                addressTableLookups?: {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[] & ({
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                } & {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                } & { [K_35 in Exclude<keyof I_1["transaction"]["message"]["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_36 in Exclude<keyof I_1["transaction"]["message"]["addressTableLookups"], keyof {
                    accountKey?: Uint8Array;
                    writableIndexes?: Uint8Array;
                    readonlyIndexes?: Uint8Array;
                }[]>]: never; };
            } & { [K_37 in Exclude<keyof I_1["transaction"]["message"], keyof Message>]: never; };
        } & { [K_38 in Exclude<keyof I_1["transaction"], keyof Transaction>]: never; };
        meta?: {
            err?: {
                err?: Uint8Array;
            };
            fee?: string;
            preBalances?: string[];
            postBalances?: string[];
            innerInstructions?: {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[];
            innerInstructionsNone?: boolean;
            logMessages?: string[];
            logMessagesNone?: boolean;
            preTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            postTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[];
            rewards?: {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[];
            loadedWritableAddresses?: Uint8Array[];
            loadedReadonlyAddresses?: Uint8Array[];
            returnData?: {
                programId?: Uint8Array;
                data?: Uint8Array;
            };
            returnDataNone?: boolean;
            computeUnitsConsumed?: string | undefined;
        } & {
            err?: {
                err?: Uint8Array;
            } & {
                err?: Uint8Array;
            } & { [K_39 in Exclude<keyof I_1["meta"]["err"], "err">]: never; };
            fee?: string;
            preBalances?: string[] & string[] & { [K_40 in Exclude<keyof I_1["meta"]["preBalances"], keyof string[]>]: never; };
            postBalances?: string[] & string[] & { [K_41 in Exclude<keyof I_1["meta"]["postBalances"], keyof string[]>]: never; };
            innerInstructions?: {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[] & ({
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            } & {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[] & ({
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                } & {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                } & { [K_42 in Exclude<keyof I_1["meta"]["innerInstructions"][number]["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_43 in Exclude<keyof I_1["meta"]["innerInstructions"][number]["instructions"], keyof {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[]>]: never; };
            } & { [K_44 in Exclude<keyof I_1["meta"]["innerInstructions"][number], keyof InnerInstructions>]: never; })[] & { [K_45 in Exclude<keyof I_1["meta"]["innerInstructions"], keyof {
                index?: number;
                instructions?: {
                    programIdIndex?: number;
                    accounts?: Uint8Array;
                    data?: Uint8Array;
                    stackHeight?: number | undefined;
                }[];
            }[]>]: never; };
            innerInstructionsNone?: boolean;
            logMessages?: string[] & string[] & { [K_46 in Exclude<keyof I_1["meta"]["logMessages"], keyof string[]>]: never; };
            logMessagesNone?: boolean;
            preTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[] & ({
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            } & {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & { [K_47 in Exclude<keyof I_1["meta"]["preTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                owner?: string;
                programId?: string;
            } & { [K_48 in Exclude<keyof I_1["meta"]["preTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_49 in Exclude<keyof I_1["meta"]["preTokenBalances"], keyof {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[]>]: never; };
            postTokenBalances?: {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[] & ({
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            } & {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                } & { [K_50 in Exclude<keyof I_1["meta"]["postTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
                owner?: string;
                programId?: string;
            } & { [K_51 in Exclude<keyof I_1["meta"]["postTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_52 in Exclude<keyof I_1["meta"]["postTokenBalances"], keyof {
                accountIndex?: number;
                mint?: string;
                uiTokenAmount?: {
                    uiAmount?: number;
                    decimals?: number;
                    amount?: string;
                    uiAmountString?: string;
                };
                owner?: string;
                programId?: string;
            }[]>]: never; };
            rewards?: {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[] & ({
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            } & {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            } & { [K_53 in Exclude<keyof I_1["meta"]["rewards"][number], keyof Reward>]: never; })[] & { [K_54 in Exclude<keyof I_1["meta"]["rewards"], keyof {
                pubkey?: string;
                lamports?: string;
                postBalance?: string;
                rewardType?: RewardType;
                commission?: string;
            }[]>]: never; };
            loadedWritableAddresses?: Uint8Array[] & Uint8Array[] & { [K_55 in Exclude<keyof I_1["meta"]["loadedWritableAddresses"], keyof Uint8Array[]>]: never; };
            loadedReadonlyAddresses?: Uint8Array[] & Uint8Array[] & { [K_56 in Exclude<keyof I_1["meta"]["loadedReadonlyAddresses"], keyof Uint8Array[]>]: never; };
            returnData?: {
                programId?: Uint8Array;
                data?: Uint8Array;
            } & {
                programId?: Uint8Array;
                data?: Uint8Array;
            } & { [K_57 in Exclude<keyof I_1["meta"]["returnData"], keyof ReturnData>]: never; };
            returnDataNone?: boolean;
            computeUnitsConsumed?: string | undefined;
        } & { [K_58 in Exclude<keyof I_1["meta"], keyof TransactionStatusMeta>]: never; };
    } & { [K_59 in Exclude<keyof I_1, keyof ConfirmedTransaction>]: never; }>(object: I_1): ConfirmedTransaction;
};
export declare const Transaction: {
    encode(message: Transaction, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Transaction;
    fromJSON(object: any): Transaction;
    toJSON(message: Transaction): unknown;
    create<I extends {
        signatures?: Uint8Array[];
        message?: {
            header?: {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            };
            accountKeys?: Uint8Array[];
            recentBlockhash?: Uint8Array;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[];
            versioned?: boolean;
            addressTableLookups?: {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[];
        };
    } & {
        signatures?: Uint8Array[] & Uint8Array[] & { [K in Exclude<keyof I["signatures"], keyof Uint8Array[]>]: never; };
        message?: {
            header?: {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            };
            accountKeys?: Uint8Array[];
            recentBlockhash?: Uint8Array;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[];
            versioned?: boolean;
            addressTableLookups?: {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[];
        } & {
            header?: {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            } & {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            } & { [K_1 in Exclude<keyof I["message"]["header"], keyof MessageHeader>]: never; };
            accountKeys?: Uint8Array[] & Uint8Array[] & { [K_2 in Exclude<keyof I["message"]["accountKeys"], keyof Uint8Array[]>]: never; };
            recentBlockhash?: Uint8Array;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[] & ({
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            } & {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            } & { [K_3 in Exclude<keyof I["message"]["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_4 in Exclude<keyof I["message"]["instructions"], keyof {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[]>]: never; };
            versioned?: boolean;
            addressTableLookups?: {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[] & ({
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            } & {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            } & { [K_5 in Exclude<keyof I["message"]["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_6 in Exclude<keyof I["message"]["addressTableLookups"], keyof {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[]>]: never; };
        } & { [K_7 in Exclude<keyof I["message"], keyof Message>]: never; };
    } & { [K_8 in Exclude<keyof I, keyof Transaction>]: never; }>(base?: I): Transaction;
    fromPartial<I_1 extends {
        signatures?: Uint8Array[];
        message?: {
            header?: {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            };
            accountKeys?: Uint8Array[];
            recentBlockhash?: Uint8Array;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[];
            versioned?: boolean;
            addressTableLookups?: {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[];
        };
    } & {
        signatures?: Uint8Array[] & Uint8Array[] & { [K_9 in Exclude<keyof I_1["signatures"], keyof Uint8Array[]>]: never; };
        message?: {
            header?: {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            };
            accountKeys?: Uint8Array[];
            recentBlockhash?: Uint8Array;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[];
            versioned?: boolean;
            addressTableLookups?: {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[];
        } & {
            header?: {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            } & {
                numRequiredSignatures?: number;
                numReadonlySignedAccounts?: number;
                numReadonlyUnsignedAccounts?: number;
            } & { [K_10 in Exclude<keyof I_1["message"]["header"], keyof MessageHeader>]: never; };
            accountKeys?: Uint8Array[] & Uint8Array[] & { [K_11 in Exclude<keyof I_1["message"]["accountKeys"], keyof Uint8Array[]>]: never; };
            recentBlockhash?: Uint8Array;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[] & ({
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            } & {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            } & { [K_12 in Exclude<keyof I_1["message"]["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_13 in Exclude<keyof I_1["message"]["instructions"], keyof {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
            }[]>]: never; };
            versioned?: boolean;
            addressTableLookups?: {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[] & ({
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            } & {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            } & { [K_14 in Exclude<keyof I_1["message"]["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_15 in Exclude<keyof I_1["message"]["addressTableLookups"], keyof {
                accountKey?: Uint8Array;
                writableIndexes?: Uint8Array;
                readonlyIndexes?: Uint8Array;
            }[]>]: never; };
        } & { [K_16 in Exclude<keyof I_1["message"], keyof Message>]: never; };
    } & { [K_17 in Exclude<keyof I_1, keyof Transaction>]: never; }>(object: I_1): Transaction;
};
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create<I extends {
        header?: {
            numRequiredSignatures?: number;
            numReadonlySignedAccounts?: number;
            numReadonlyUnsignedAccounts?: number;
        };
        accountKeys?: Uint8Array[];
        recentBlockhash?: Uint8Array;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        }[];
        versioned?: boolean;
        addressTableLookups?: {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        }[];
    } & {
        header?: {
            numRequiredSignatures?: number;
            numReadonlySignedAccounts?: number;
            numReadonlyUnsignedAccounts?: number;
        } & {
            numRequiredSignatures?: number;
            numReadonlySignedAccounts?: number;
            numReadonlyUnsignedAccounts?: number;
        } & { [K in Exclude<keyof I["header"], keyof MessageHeader>]: never; };
        accountKeys?: Uint8Array[] & Uint8Array[] & { [K_1 in Exclude<keyof I["accountKeys"], keyof Uint8Array[]>]: never; };
        recentBlockhash?: Uint8Array;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        }[] & ({
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        } & {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        } & { [K_2 in Exclude<keyof I["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_3 in Exclude<keyof I["instructions"], keyof {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        }[]>]: never; };
        versioned?: boolean;
        addressTableLookups?: {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        }[] & ({
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        } & {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        } & { [K_4 in Exclude<keyof I["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_5 in Exclude<keyof I["addressTableLookups"], keyof {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        }[]>]: never; };
    } & { [K_6 in Exclude<keyof I, keyof Message>]: never; }>(base?: I): Message;
    fromPartial<I_1 extends {
        header?: {
            numRequiredSignatures?: number;
            numReadonlySignedAccounts?: number;
            numReadonlyUnsignedAccounts?: number;
        };
        accountKeys?: Uint8Array[];
        recentBlockhash?: Uint8Array;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        }[];
        versioned?: boolean;
        addressTableLookups?: {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        }[];
    } & {
        header?: {
            numRequiredSignatures?: number;
            numReadonlySignedAccounts?: number;
            numReadonlyUnsignedAccounts?: number;
        } & {
            numRequiredSignatures?: number;
            numReadonlySignedAccounts?: number;
            numReadonlyUnsignedAccounts?: number;
        } & { [K_7 in Exclude<keyof I_1["header"], keyof MessageHeader>]: never; };
        accountKeys?: Uint8Array[] & Uint8Array[] & { [K_8 in Exclude<keyof I_1["accountKeys"], keyof Uint8Array[]>]: never; };
        recentBlockhash?: Uint8Array;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        }[] & ({
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        } & {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        } & { [K_9 in Exclude<keyof I_1["instructions"][number], keyof CompiledInstruction>]: never; })[] & { [K_10 in Exclude<keyof I_1["instructions"], keyof {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
        }[]>]: never; };
        versioned?: boolean;
        addressTableLookups?: {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        }[] & ({
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        } & {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        } & { [K_11 in Exclude<keyof I_1["addressTableLookups"][number], keyof MessageAddressTableLookup>]: never; })[] & { [K_12 in Exclude<keyof I_1["addressTableLookups"], keyof {
            accountKey?: Uint8Array;
            writableIndexes?: Uint8Array;
            readonlyIndexes?: Uint8Array;
        }[]>]: never; };
    } & { [K_13 in Exclude<keyof I_1, keyof Message>]: never; }>(object: I_1): Message;
};
export declare const MessageHeader: {
    encode(message: MessageHeader, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MessageHeader;
    fromJSON(object: any): MessageHeader;
    toJSON(message: MessageHeader): unknown;
    create<I extends {
        numRequiredSignatures?: number;
        numReadonlySignedAccounts?: number;
        numReadonlyUnsignedAccounts?: number;
    } & {
        numRequiredSignatures?: number;
        numReadonlySignedAccounts?: number;
        numReadonlyUnsignedAccounts?: number;
    } & { [K in Exclude<keyof I, keyof MessageHeader>]: never; }>(base?: I): MessageHeader;
    fromPartial<I_1 extends {
        numRequiredSignatures?: number;
        numReadonlySignedAccounts?: number;
        numReadonlyUnsignedAccounts?: number;
    } & {
        numRequiredSignatures?: number;
        numReadonlySignedAccounts?: number;
        numReadonlyUnsignedAccounts?: number;
    } & { [K_1 in Exclude<keyof I_1, keyof MessageHeader>]: never; }>(object: I_1): MessageHeader;
};
export declare const MessageAddressTableLookup: {
    encode(message: MessageAddressTableLookup, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MessageAddressTableLookup;
    fromJSON(object: any): MessageAddressTableLookup;
    toJSON(message: MessageAddressTableLookup): unknown;
    create<I extends {
        accountKey?: Uint8Array;
        writableIndexes?: Uint8Array;
        readonlyIndexes?: Uint8Array;
    } & {
        accountKey?: Uint8Array;
        writableIndexes?: Uint8Array;
        readonlyIndexes?: Uint8Array;
    } & { [K in Exclude<keyof I, keyof MessageAddressTableLookup>]: never; }>(base?: I): MessageAddressTableLookup;
    fromPartial<I_1 extends {
        accountKey?: Uint8Array;
        writableIndexes?: Uint8Array;
        readonlyIndexes?: Uint8Array;
    } & {
        accountKey?: Uint8Array;
        writableIndexes?: Uint8Array;
        readonlyIndexes?: Uint8Array;
    } & { [K_1 in Exclude<keyof I_1, keyof MessageAddressTableLookup>]: never; }>(object: I_1): MessageAddressTableLookup;
};
export declare const TransactionStatusMeta: {
    encode(message: TransactionStatusMeta, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TransactionStatusMeta;
    fromJSON(object: any): TransactionStatusMeta;
    toJSON(message: TransactionStatusMeta): unknown;
    create<I extends {
        err?: {
            err?: Uint8Array;
        };
        fee?: string;
        preBalances?: string[];
        postBalances?: string[];
        innerInstructions?: {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        }[];
        innerInstructionsNone?: boolean;
        logMessages?: string[];
        logMessagesNone?: boolean;
        preTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[];
        postTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[];
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[];
        loadedWritableAddresses?: Uint8Array[];
        loadedReadonlyAddresses?: Uint8Array[];
        returnData?: {
            programId?: Uint8Array;
            data?: Uint8Array;
        };
        returnDataNone?: boolean;
        computeUnitsConsumed?: string | undefined;
    } & {
        err?: {
            err?: Uint8Array;
        } & {
            err?: Uint8Array;
        } & { [K in Exclude<keyof I["err"], "err">]: never; };
        fee?: string;
        preBalances?: string[] & string[] & { [K_1 in Exclude<keyof I["preBalances"], keyof string[]>]: never; };
        postBalances?: string[] & string[] & { [K_2 in Exclude<keyof I["postBalances"], keyof string[]>]: never; };
        innerInstructions?: {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        }[] & ({
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        } & {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[] & ({
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            } & {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            } & { [K_3 in Exclude<keyof I["innerInstructions"][number]["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_4 in Exclude<keyof I["innerInstructions"][number]["instructions"], keyof {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[]>]: never; };
        } & { [K_5 in Exclude<keyof I["innerInstructions"][number], keyof InnerInstructions>]: never; })[] & { [K_6 in Exclude<keyof I["innerInstructions"], keyof {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        }[]>]: never; };
        innerInstructionsNone?: boolean;
        logMessages?: string[] & string[] & { [K_7 in Exclude<keyof I["logMessages"], keyof string[]>]: never; };
        logMessagesNone?: boolean;
        preTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[] & ({
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        } & {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & { [K_8 in Exclude<keyof I["preTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
            owner?: string;
            programId?: string;
        } & { [K_9 in Exclude<keyof I["preTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_10 in Exclude<keyof I["preTokenBalances"], keyof {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[]>]: never; };
        postTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[] & ({
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        } & {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & { [K_11 in Exclude<keyof I["postTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
            owner?: string;
            programId?: string;
        } & { [K_12 in Exclude<keyof I["postTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_13 in Exclude<keyof I["postTokenBalances"], keyof {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[]>]: never; };
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[] & ({
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & { [K_14 in Exclude<keyof I["rewards"][number], keyof Reward>]: never; })[] & { [K_15 in Exclude<keyof I["rewards"], keyof {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[]>]: never; };
        loadedWritableAddresses?: Uint8Array[] & Uint8Array[] & { [K_16 in Exclude<keyof I["loadedWritableAddresses"], keyof Uint8Array[]>]: never; };
        loadedReadonlyAddresses?: Uint8Array[] & Uint8Array[] & { [K_17 in Exclude<keyof I["loadedReadonlyAddresses"], keyof Uint8Array[]>]: never; };
        returnData?: {
            programId?: Uint8Array;
            data?: Uint8Array;
        } & {
            programId?: Uint8Array;
            data?: Uint8Array;
        } & { [K_18 in Exclude<keyof I["returnData"], keyof ReturnData>]: never; };
        returnDataNone?: boolean;
        computeUnitsConsumed?: string | undefined;
    } & { [K_19 in Exclude<keyof I, keyof TransactionStatusMeta>]: never; }>(base?: I): TransactionStatusMeta;
    fromPartial<I_1 extends {
        err?: {
            err?: Uint8Array;
        };
        fee?: string;
        preBalances?: string[];
        postBalances?: string[];
        innerInstructions?: {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        }[];
        innerInstructionsNone?: boolean;
        logMessages?: string[];
        logMessagesNone?: boolean;
        preTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[];
        postTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[];
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[];
        loadedWritableAddresses?: Uint8Array[];
        loadedReadonlyAddresses?: Uint8Array[];
        returnData?: {
            programId?: Uint8Array;
            data?: Uint8Array;
        };
        returnDataNone?: boolean;
        computeUnitsConsumed?: string | undefined;
    } & {
        err?: {
            err?: Uint8Array;
        } & {
            err?: Uint8Array;
        } & { [K_20 in Exclude<keyof I_1["err"], "err">]: never; };
        fee?: string;
        preBalances?: string[] & string[] & { [K_21 in Exclude<keyof I_1["preBalances"], keyof string[]>]: never; };
        postBalances?: string[] & string[] & { [K_22 in Exclude<keyof I_1["postBalances"], keyof string[]>]: never; };
        innerInstructions?: {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        }[] & ({
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        } & {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[] & ({
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            } & {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            } & { [K_23 in Exclude<keyof I_1["innerInstructions"][number]["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_24 in Exclude<keyof I_1["innerInstructions"][number]["instructions"], keyof {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[]>]: never; };
        } & { [K_25 in Exclude<keyof I_1["innerInstructions"][number], keyof InnerInstructions>]: never; })[] & { [K_26 in Exclude<keyof I_1["innerInstructions"], keyof {
            index?: number;
            instructions?: {
                programIdIndex?: number;
                accounts?: Uint8Array;
                data?: Uint8Array;
                stackHeight?: number | undefined;
            }[];
        }[]>]: never; };
        innerInstructionsNone?: boolean;
        logMessages?: string[] & string[] & { [K_27 in Exclude<keyof I_1["logMessages"], keyof string[]>]: never; };
        logMessagesNone?: boolean;
        preTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[] & ({
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        } & {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & { [K_28 in Exclude<keyof I_1["preTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
            owner?: string;
            programId?: string;
        } & { [K_29 in Exclude<keyof I_1["preTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_30 in Exclude<keyof I_1["preTokenBalances"], keyof {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[]>]: never; };
        postTokenBalances?: {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[] & ({
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        } & {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            } & { [K_31 in Exclude<keyof I_1["postTokenBalances"][number]["uiTokenAmount"], keyof UiTokenAmount>]: never; };
            owner?: string;
            programId?: string;
        } & { [K_32 in Exclude<keyof I_1["postTokenBalances"][number], keyof TokenBalance>]: never; })[] & { [K_33 in Exclude<keyof I_1["postTokenBalances"], keyof {
            accountIndex?: number;
            mint?: string;
            uiTokenAmount?: {
                uiAmount?: number;
                decimals?: number;
                amount?: string;
                uiAmountString?: string;
            };
            owner?: string;
            programId?: string;
        }[]>]: never; };
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[] & ({
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & { [K_34 in Exclude<keyof I_1["rewards"][number], keyof Reward>]: never; })[] & { [K_35 in Exclude<keyof I_1["rewards"], keyof {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[]>]: never; };
        loadedWritableAddresses?: Uint8Array[] & Uint8Array[] & { [K_36 in Exclude<keyof I_1["loadedWritableAddresses"], keyof Uint8Array[]>]: never; };
        loadedReadonlyAddresses?: Uint8Array[] & Uint8Array[] & { [K_37 in Exclude<keyof I_1["loadedReadonlyAddresses"], keyof Uint8Array[]>]: never; };
        returnData?: {
            programId?: Uint8Array;
            data?: Uint8Array;
        } & {
            programId?: Uint8Array;
            data?: Uint8Array;
        } & { [K_38 in Exclude<keyof I_1["returnData"], keyof ReturnData>]: never; };
        returnDataNone?: boolean;
        computeUnitsConsumed?: string | undefined;
    } & { [K_39 in Exclude<keyof I_1, keyof TransactionStatusMeta>]: never; }>(object: I_1): TransactionStatusMeta;
};
export declare const TransactionError: {
    encode(message: TransactionError, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TransactionError;
    fromJSON(object: any): TransactionError;
    toJSON(message: TransactionError): unknown;
    create<I extends {
        err?: Uint8Array;
    } & {
        err?: Uint8Array;
    } & { [K in Exclude<keyof I, "err">]: never; }>(base?: I): TransactionError;
    fromPartial<I_1 extends {
        err?: Uint8Array;
    } & {
        err?: Uint8Array;
    } & { [K_1 in Exclude<keyof I_1, "err">]: never; }>(object: I_1): TransactionError;
};
export declare const InnerInstructions: {
    encode(message: InnerInstructions, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InnerInstructions;
    fromJSON(object: any): InnerInstructions;
    toJSON(message: InnerInstructions): unknown;
    create<I extends {
        index?: number;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        }[];
    } & {
        index?: number;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        }[] & ({
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        } & {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        } & { [K in Exclude<keyof I["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_1 in Exclude<keyof I["instructions"], keyof {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        }[]>]: never; };
    } & { [K_2 in Exclude<keyof I, keyof InnerInstructions>]: never; }>(base?: I): InnerInstructions;
    fromPartial<I_1 extends {
        index?: number;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        }[];
    } & {
        index?: number;
        instructions?: {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        }[] & ({
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        } & {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        } & { [K_3 in Exclude<keyof I_1["instructions"][number], keyof InnerInstruction>]: never; })[] & { [K_4 in Exclude<keyof I_1["instructions"], keyof {
            programIdIndex?: number;
            accounts?: Uint8Array;
            data?: Uint8Array;
            stackHeight?: number | undefined;
        }[]>]: never; };
    } & { [K_5 in Exclude<keyof I_1, keyof InnerInstructions>]: never; }>(object: I_1): InnerInstructions;
};
export declare const InnerInstruction: {
    encode(message: InnerInstruction, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InnerInstruction;
    fromJSON(object: any): InnerInstruction;
    toJSON(message: InnerInstruction): unknown;
    create<I extends {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
        stackHeight?: number | undefined;
    } & {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
        stackHeight?: number | undefined;
    } & { [K in Exclude<keyof I, keyof InnerInstruction>]: never; }>(base?: I): InnerInstruction;
    fromPartial<I_1 extends {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
        stackHeight?: number | undefined;
    } & {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
        stackHeight?: number | undefined;
    } & { [K_1 in Exclude<keyof I_1, keyof InnerInstruction>]: never; }>(object: I_1): InnerInstruction;
};
export declare const CompiledInstruction: {
    encode(message: CompiledInstruction, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CompiledInstruction;
    fromJSON(object: any): CompiledInstruction;
    toJSON(message: CompiledInstruction): unknown;
    create<I extends {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
    } & {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
    } & { [K in Exclude<keyof I, keyof CompiledInstruction>]: never; }>(base?: I): CompiledInstruction;
    fromPartial<I_1 extends {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
    } & {
        programIdIndex?: number;
        accounts?: Uint8Array;
        data?: Uint8Array;
    } & { [K_1 in Exclude<keyof I_1, keyof CompiledInstruction>]: never; }>(object: I_1): CompiledInstruction;
};
export declare const TokenBalance: {
    encode(message: TokenBalance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TokenBalance;
    fromJSON(object: any): TokenBalance;
    toJSON(message: TokenBalance): unknown;
    create<I extends {
        accountIndex?: number;
        mint?: string;
        uiTokenAmount?: {
            uiAmount?: number;
            decimals?: number;
            amount?: string;
            uiAmountString?: string;
        };
        owner?: string;
        programId?: string;
    } & {
        accountIndex?: number;
        mint?: string;
        uiTokenAmount?: {
            uiAmount?: number;
            decimals?: number;
            amount?: string;
            uiAmountString?: string;
        } & {
            uiAmount?: number;
            decimals?: number;
            amount?: string;
            uiAmountString?: string;
        } & { [K in Exclude<keyof I["uiTokenAmount"], keyof UiTokenAmount>]: never; };
        owner?: string;
        programId?: string;
    } & { [K_1 in Exclude<keyof I, keyof TokenBalance>]: never; }>(base?: I): TokenBalance;
    fromPartial<I_1 extends {
        accountIndex?: number;
        mint?: string;
        uiTokenAmount?: {
            uiAmount?: number;
            decimals?: number;
            amount?: string;
            uiAmountString?: string;
        };
        owner?: string;
        programId?: string;
    } & {
        accountIndex?: number;
        mint?: string;
        uiTokenAmount?: {
            uiAmount?: number;
            decimals?: number;
            amount?: string;
            uiAmountString?: string;
        } & {
            uiAmount?: number;
            decimals?: number;
            amount?: string;
            uiAmountString?: string;
        } & { [K_2 in Exclude<keyof I_1["uiTokenAmount"], keyof UiTokenAmount>]: never; };
        owner?: string;
        programId?: string;
    } & { [K_3 in Exclude<keyof I_1, keyof TokenBalance>]: never; }>(object: I_1): TokenBalance;
};
export declare const UiTokenAmount: {
    encode(message: UiTokenAmount, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): UiTokenAmount;
    fromJSON(object: any): UiTokenAmount;
    toJSON(message: UiTokenAmount): unknown;
    create<I extends {
        uiAmount?: number;
        decimals?: number;
        amount?: string;
        uiAmountString?: string;
    } & {
        uiAmount?: number;
        decimals?: number;
        amount?: string;
        uiAmountString?: string;
    } & { [K in Exclude<keyof I, keyof UiTokenAmount>]: never; }>(base?: I): UiTokenAmount;
    fromPartial<I_1 extends {
        uiAmount?: number;
        decimals?: number;
        amount?: string;
        uiAmountString?: string;
    } & {
        uiAmount?: number;
        decimals?: number;
        amount?: string;
        uiAmountString?: string;
    } & { [K_1 in Exclude<keyof I_1, keyof UiTokenAmount>]: never; }>(object: I_1): UiTokenAmount;
};
export declare const ReturnData: {
    encode(message: ReturnData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ReturnData;
    fromJSON(object: any): ReturnData;
    toJSON(message: ReturnData): unknown;
    create<I extends {
        programId?: Uint8Array;
        data?: Uint8Array;
    } & {
        programId?: Uint8Array;
        data?: Uint8Array;
    } & { [K in Exclude<keyof I, keyof ReturnData>]: never; }>(base?: I): ReturnData;
    fromPartial<I_1 extends {
        programId?: Uint8Array;
        data?: Uint8Array;
    } & {
        programId?: Uint8Array;
        data?: Uint8Array;
    } & { [K_1 in Exclude<keyof I_1, keyof ReturnData>]: never; }>(object: I_1): ReturnData;
};
export declare const Reward: {
    encode(message: Reward, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Reward;
    fromJSON(object: any): Reward;
    toJSON(message: Reward): unknown;
    create<I extends {
        pubkey?: string;
        lamports?: string;
        postBalance?: string;
        rewardType?: RewardType;
        commission?: string;
    } & {
        pubkey?: string;
        lamports?: string;
        postBalance?: string;
        rewardType?: RewardType;
        commission?: string;
    } & { [K in Exclude<keyof I, keyof Reward>]: never; }>(base?: I): Reward;
    fromPartial<I_1 extends {
        pubkey?: string;
        lamports?: string;
        postBalance?: string;
        rewardType?: RewardType;
        commission?: string;
    } & {
        pubkey?: string;
        lamports?: string;
        postBalance?: string;
        rewardType?: RewardType;
        commission?: string;
    } & { [K_1 in Exclude<keyof I_1, keyof Reward>]: never; }>(object: I_1): Reward;
};
export declare const Rewards: {
    encode(message: Rewards, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Rewards;
    fromJSON(object: any): Rewards;
    toJSON(message: Rewards): unknown;
    create<I extends {
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[];
        numPartitions?: {
            numPartitions?: string;
        };
    } & {
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[] & ({
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & { [K in Exclude<keyof I["rewards"][number], keyof Reward>]: never; })[] & { [K_1 in Exclude<keyof I["rewards"], keyof {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[]>]: never; };
        numPartitions?: {
            numPartitions?: string;
        } & {
            numPartitions?: string;
        } & { [K_2 in Exclude<keyof I["numPartitions"], "numPartitions">]: never; };
    } & { [K_3 in Exclude<keyof I, keyof Rewards>]: never; }>(base?: I): Rewards;
    fromPartial<I_1 extends {
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[];
        numPartitions?: {
            numPartitions?: string;
        };
    } & {
        rewards?: {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[] & ({
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        } & { [K_4 in Exclude<keyof I_1["rewards"][number], keyof Reward>]: never; })[] & { [K_5 in Exclude<keyof I_1["rewards"], keyof {
            pubkey?: string;
            lamports?: string;
            postBalance?: string;
            rewardType?: RewardType;
            commission?: string;
        }[]>]: never; };
        numPartitions?: {
            numPartitions?: string;
        } & {
            numPartitions?: string;
        } & { [K_6 in Exclude<keyof I_1["numPartitions"], "numPartitions">]: never; };
    } & { [K_7 in Exclude<keyof I_1, keyof Rewards>]: never; }>(object: I_1): Rewards;
};
export declare const UnixTimestamp: {
    encode(message: UnixTimestamp, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): UnixTimestamp;
    fromJSON(object: any): UnixTimestamp;
    toJSON(message: UnixTimestamp): unknown;
    create<I extends {
        timestamp?: string;
    } & {
        timestamp?: string;
    } & { [K in Exclude<keyof I, "timestamp">]: never; }>(base?: I): UnixTimestamp;
    fromPartial<I_1 extends {
        timestamp?: string;
    } & {
        timestamp?: string;
    } & { [K_1 in Exclude<keyof I_1, "timestamp">]: never; }>(object: I_1): UnixTimestamp;
};
export declare const BlockHeight: {
    encode(message: BlockHeight, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockHeight;
    fromJSON(object: any): BlockHeight;
    toJSON(message: BlockHeight): unknown;
    create<I extends {
        blockHeight?: string;
    } & {
        blockHeight?: string;
    } & { [K in Exclude<keyof I, "blockHeight">]: never; }>(base?: I): BlockHeight;
    fromPartial<I_1 extends {
        blockHeight?: string;
    } & {
        blockHeight?: string;
    } & { [K_1 in Exclude<keyof I_1, "blockHeight">]: never; }>(object: I_1): BlockHeight;
};
export declare const NumPartitions: {
    encode(message: NumPartitions, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): NumPartitions;
    fromJSON(object: any): NumPartitions;
    toJSON(message: NumPartitions): unknown;
    create<I extends {
        numPartitions?: string;
    } & {
        numPartitions?: string;
    } & { [K in Exclude<keyof I, "numPartitions">]: never; }>(base?: I): NumPartitions;
    fromPartial<I_1 extends {
        numPartitions?: string;
    } & {
        numPartitions?: string;
    } & { [K_1 in Exclude<keyof I_1, "numPartitions">]: never; }>(object: I_1): NumPartitions;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P : P & {
    [K in keyof P]: Exact<P[K], I[K]>;
} & {
    [K in Exclude<keyof I, KeysOfUnion<P>>]: never;
};
export {};
