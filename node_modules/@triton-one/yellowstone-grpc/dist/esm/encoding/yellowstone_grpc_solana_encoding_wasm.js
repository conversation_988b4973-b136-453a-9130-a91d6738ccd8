
let imports = {};
imports['__wbindgen_placeholder__'] = module.exports;
let wasm;
const { TextDecoder, TextEncoder } = require(`util`);

function addToExternrefTable0(obj) {
    const idx = wasm.__externref_table_alloc();
    wasm.__wbindgen_export_2.set(idx, obj);
    return idx;
}

function handleError(f, args) {
    try {
        return f.apply(this, args);
    } catch (e) {
        const idx = addToExternrefTable0(e);
        wasm.__wbindgen_exn_store(idx);
    }
}

let cachedTextDecoder = new TextDecoder('utf-8', { ignoreBOM: true, fatal: true });

cachedTextDecoder.decode();

let cachedUint8ArrayMemory0 = null;

function getUint8ArrayMemory0() {
    if (cachedUint8ArrayMemory0 === null || cachedUint8ArrayMemory0.byteLength === 0) {
        cachedUint8ArrayMemory0 = new Uint8Array(wasm.memory.buffer);
    }
    return cachedUint8ArrayMemory0;
}

function getStringFromWasm0(ptr, len) {
    ptr = ptr >>> 0;
    return cachedTextDecoder.decode(getUint8ArrayMemory0().subarray(ptr, ptr + len));
}

function getArrayU8FromWasm0(ptr, len) {
    ptr = ptr >>> 0;
    return getUint8ArrayMemory0().subarray(ptr / 1, ptr / 1 + len);
}

let WASM_VECTOR_LEN = 0;

let cachedTextEncoder = new TextEncoder('utf-8');

const encodeString = (typeof cachedTextEncoder.encodeInto === 'function'
    ? function (arg, view) {
    return cachedTextEncoder.encodeInto(arg, view);
}
    : function (arg, view) {
    const buf = cachedTextEncoder.encode(arg);
    view.set(buf);
    return {
        read: arg.length,
        written: buf.length
    };
});

function passStringToWasm0(arg, malloc, realloc) {

    if (realloc === undefined) {
        const buf = cachedTextEncoder.encode(arg);
        const ptr = malloc(buf.length, 1) >>> 0;
        getUint8ArrayMemory0().subarray(ptr, ptr + buf.length).set(buf);
        WASM_VECTOR_LEN = buf.length;
        return ptr;
    }

    let len = arg.length;
    let ptr = malloc(len, 1) >>> 0;

    const mem = getUint8ArrayMemory0();

    let offset = 0;

    for (; offset < len; offset++) {
        const code = arg.charCodeAt(offset);
        if (code > 0x7F) break;
        mem[ptr + offset] = code;
    }

    if (offset !== len) {
        if (offset !== 0) {
            arg = arg.slice(offset);
        }
        ptr = realloc(ptr, len, len = offset + arg.length * 3, 1) >>> 0;
        const view = getUint8ArrayMemory0().subarray(ptr + offset, ptr + len);
        const ret = encodeString(arg, view);

        offset += ret.written;
        ptr = realloc(ptr, len, offset, 1) >>> 0;
    }

    WASM_VECTOR_LEN = offset;
    return ptr;
}

let cachedDataViewMemory0 = null;

function getDataViewMemory0() {
    if (cachedDataViewMemory0 === null || cachedDataViewMemory0.buffer.detached === true || (cachedDataViewMemory0.buffer.detached === undefined && cachedDataViewMemory0.buffer !== wasm.memory.buffer)) {
        cachedDataViewMemory0 = new DataView(wasm.memory.buffer);
    }
    return cachedDataViewMemory0;
}

function isLikeNone(x) {
    return x === undefined || x === null;
}

function debugString(val) {
    // primitive types
    const type = typeof val;
    if (type == 'number' || type == 'boolean' || val == null) {
        return  `${val}`;
    }
    if (type == 'string') {
        return `"${val}"`;
    }
    if (type == 'symbol') {
        const description = val.description;
        if (description == null) {
            return 'Symbol';
        } else {
            return `Symbol(${description})`;
        }
    }
    if (type == 'function') {
        const name = val.name;
        if (typeof name == 'string' && name.length > 0) {
            return `Function(${name})`;
        } else {
            return 'Function';
        }
    }
    // objects
    if (Array.isArray(val)) {
        const length = val.length;
        let debug = '[';
        if (length > 0) {
            debug += debugString(val[0]);
        }
        for(let i = 1; i < length; i++) {
            debug += ', ' + debugString(val[i]);
        }
        debug += ']';
        return debug;
    }
    // Test for built-in
    const builtInMatches = /\[object ([^\]]+)\]/.exec(toString.call(val));
    let className;
    if (builtInMatches && builtInMatches.length > 1) {
        className = builtInMatches[1];
    } else {
        // Failed to match the standard '[object ClassName]'
        return toString.call(val);
    }
    if (className == 'Object') {
        // we're a user defined class or Object
        // JSON.stringify avoids problems with cycles, and is generally much
        // easier than looping through ownProperties of `val`.
        try {
            return 'Object(' + JSON.stringify(val) + ')';
        } catch (_) {
            return 'Object';
        }
    }
    // errors
    if (val instanceof Error) {
        return `${val.name}: ${val.message}\n${val.stack}`;
    }
    // TODO we could test for more things here, like `Set`s and `Map`s.
    return className;
}

function passArray8ToWasm0(arg, malloc) {
    const ptr = malloc(arg.length * 1, 1) >>> 0;
    getUint8ArrayMemory0().set(arg, ptr / 1);
    WASM_VECTOR_LEN = arg.length;
    return ptr;
}

function takeFromExternrefTable0(idx) {
    const value = wasm.__wbindgen_export_2.get(idx);
    wasm.__externref_table_dealloc(idx);
    return value;
}
/**
 * @param {Uint8Array} data
 * @param {WasmUiTransactionEncoding} encoding
 * @param {number | null | undefined} max_supported_transaction_version
 * @param {boolean} show_rewards
 * @returns {string}
 */
module.exports.encode_tx = function(data, encoding, max_supported_transaction_version, show_rewards) {
    let deferred3_0;
    let deferred3_1;
    try {
        const ptr0 = passArray8ToWasm0(data, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.encode_tx(ptr0, len0, encoding, isLikeNone(max_supported_transaction_version) ? 0xFFFFFF : max_supported_transaction_version, show_rewards);
        var ptr2 = ret[0];
        var len2 = ret[1];
        if (ret[3]) {
            ptr2 = 0; len2 = 0;
            throw takeFromExternrefTable0(ret[2]);
        }
        deferred3_0 = ptr2;
        deferred3_1 = len2;
        return getStringFromWasm0(ptr2, len2);
    } finally {
        wasm.__wbindgen_free(deferred3_0, deferred3_1, 1);
    }
};

/**
 * @param {Uint8Array} err
 * @returns {string}
 */
module.exports.decode_tx_error = function(err) {
    let deferred3_0;
    let deferred3_1;
    try {
        const ptr0 = passArray8ToWasm0(err, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.decode_tx_error(ptr0, len0);
        var ptr2 = ret[0];
        var len2 = ret[1];
        if (ret[3]) {
            ptr2 = 0; len2 = 0;
            throw takeFromExternrefTable0(ret[2]);
        }
        deferred3_0 = ptr2;
        deferred3_1 = len2;
        return getStringFromWasm0(ptr2, len2);
    } finally {
        wasm.__wbindgen_free(deferred3_0, deferred3_1, 1);
    }
};

function _assertClass(instance, klass) {
    if (!(instance instanceof klass)) {
        throw new Error(`expected instance of ${klass.name}`);
    }
}
/**
 * Initialize Javascript logging and panic handler
 */
module.exports.solana_program_init = function() {
    wasm.solana_program_init();
};

function passArrayJsValueToWasm0(array, malloc) {
    const ptr = malloc(array.length * 4, 4) >>> 0;
    for (let i = 0; i < array.length; i++) {
        const add = addToExternrefTable0(array[i]);
        getDataViewMemory0().setUint32(ptr + 4 * i, add, true);
    }
    WASM_VECTOR_LEN = array.length;
    return ptr;
}
/**
 * @enum {0 | 1 | 2 | 3 | 4}
 */
module.exports.WasmUiTransactionEncoding = Object.freeze({
    Binary: 0, "0": "Binary",
    Base64: 1, "1": "Base64",
    Base58: 2, "2": "Base58",
    Json: 3, "3": "Json",
    JsonParsed: 4, "4": "JsonParsed",
});

const AeCiphertextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_aeciphertext_free(ptr >>> 0, 1));
/**
 * Authenticated encryption nonce and ciphertext
 */
class AeCiphertext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(AeCiphertext.prototype);
        obj.__wbg_ptr = ptr;
        AeCiphertextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        AeCiphertextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_aeciphertext_free(ptr, 0);
    }
}
module.exports.AeCiphertext = AeCiphertext;

const AeKeyFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_aekey_free(ptr >>> 0, 1));

class AeKey {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(AeKey.prototype);
        obj.__wbg_ptr = ptr;
        AeKeyFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        AeKeyFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_aekey_free(ptr, 0);
    }
    /**
     * Generates a random authenticated encryption key.
     *
     * This function is randomized. It internally samples a scalar element using `OsRng`.
     * @returns {AeKey}
     */
    static newRand() {
        const ret = wasm.aekey_newRand();
        return AeKey.__wrap(ret);
    }
    /**
     * Encrypts an amount under the authenticated encryption key.
     * @param {bigint} amount
     * @returns {AeCiphertext}
     */
    encrypt(amount) {
        const ret = wasm.aekey_encrypt(this.__wbg_ptr, amount);
        return AeCiphertext.__wrap(ret);
    }
    /**
     * @param {AeCiphertext} ciphertext
     * @returns {bigint | undefined}
     */
    decrypt(ciphertext) {
        _assertClass(ciphertext, AeCiphertext);
        const ret = wasm.aekey_decrypt(this.__wbg_ptr, ciphertext.__wbg_ptr);
        return ret[0] === 0 ? undefined : BigInt.asUintN(64, ret[1]);
    }
}
module.exports.AeKey = AeKey;

const BatchedGroupedCiphertext2HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_batchedgroupedciphertext2handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * Batched grouped ciphertext validity proof with two handles.
 *
 * A batched grouped ciphertext validity proof certifies the validity of two instances of a
 * standard ciphertext validity proof. An instance of a standard validity proof consists of one
 * ciphertext and two decryption handles: `(commitment, first_handle, second_handle)`. An
 * instance of a batched ciphertext validity proof is a pair `(commitment_0,
 * first_handle_0, second_handle_0)` and `(commitment_1, first_handle_1,
 * second_handle_1)`. The proof certifies the analogous decryptable properties for each one of
 * these pairs of commitment and decryption handles.
 */
class BatchedGroupedCiphertext2HandlesValidityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        BatchedGroupedCiphertext2HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_batchedgroupedciphertext2handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.BatchedGroupedCiphertext2HandlesValidityProof = BatchedGroupedCiphertext2HandlesValidityProof;

const BatchedGroupedCiphertext2HandlesValidityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_batchedgroupedciphertext2handlesvalidityproofcontext_free(ptr >>> 0, 1));

class BatchedGroupedCiphertext2HandlesValidityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(BatchedGroupedCiphertext2HandlesValidityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        BatchedGroupedCiphertext2HandlesValidityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        BatchedGroupedCiphertext2HandlesValidityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_batchedgroupedciphertext2handlesvalidityproofcontext_free(ptr, 0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get first_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set first_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get second_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set second_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedElGamalCiphertext2Handles}
     */
    get grouped_ciphertext_lo() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr);
        return PodGroupedElGamalCiphertext2Handles.__wrap(ret);
    }
    /**
     * @param {PodGroupedElGamalCiphertext2Handles} arg0
     */
    set grouped_ciphertext_lo(arg0) {
        _assertClass(arg0, PodGroupedElGamalCiphertext2Handles);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedElGamalCiphertext2Handles}
     */
    get grouped_ciphertext_hi() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_hi(this.__wbg_ptr);
        return PodGroupedElGamalCiphertext2Handles.__wrap(ret);
    }
    /**
     * @param {PodGroupedElGamalCiphertext2Handles} arg0
     */
    set grouped_ciphertext_hi(arg0) {
        _assertClass(arg0, PodGroupedElGamalCiphertext2Handles);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_hi(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.batchedgroupedciphertext2handlesvalidityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {BatchedGroupedCiphertext2HandlesValidityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.batchedgroupedciphertext2handlesvalidityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return BatchedGroupedCiphertext2HandlesValidityProofContext.__wrap(ret[0]);
    }
}
module.exports.BatchedGroupedCiphertext2HandlesValidityProofContext = BatchedGroupedCiphertext2HandlesValidityProofContext;

const BatchedGroupedCiphertext2HandlesValidityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_batchedgroupedciphertext2handlesvalidityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyBatchedGroupedCiphertextValidity` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class BatchedGroupedCiphertext2HandlesValidityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(BatchedGroupedCiphertext2HandlesValidityProofData.prototype);
        obj.__wbg_ptr = ptr;
        BatchedGroupedCiphertext2HandlesValidityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        BatchedGroupedCiphertext2HandlesValidityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_batchedgroupedciphertext2handlesvalidityproofdata_free(ptr, 0);
    }
    /**
     * @returns {BatchedGroupedCiphertext2HandlesValidityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofdata_context(this.__wbg_ptr);
        return BatchedGroupedCiphertext2HandlesValidityProofContext.__wrap(ret);
    }
    /**
     * @param {BatchedGroupedCiphertext2HandlesValidityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, BatchedGroupedCiphertext2HandlesValidityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodBatchedGroupedCiphertext2HandlesValidityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofdata_proof(this.__wbg_ptr);
        return PodBatchedGroupedCiphertext2HandlesValidityProof.__wrap(ret);
    }
    /**
     * @param {PodBatchedGroupedCiphertext2HandlesValidityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodBatchedGroupedCiphertext2HandlesValidityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {GroupedElGamalCiphertext2Handles} grouped_ciphertext_lo
     * @param {GroupedElGamalCiphertext2Handles} grouped_ciphertext_hi
     * @param {bigint} amount_lo
     * @param {bigint} amount_hi
     * @param {PedersenOpening} opening_lo
     * @param {PedersenOpening} opening_hi
     * @returns {BatchedGroupedCiphertext2HandlesValidityProofData}
     */
    static new(first_pubkey, second_pubkey, grouped_ciphertext_lo, grouped_ciphertext_hi, amount_lo, amount_hi, opening_lo, opening_hi) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(grouped_ciphertext_lo, GroupedElGamalCiphertext2Handles);
        _assertClass(grouped_ciphertext_hi, GroupedElGamalCiphertext2Handles);
        _assertClass(opening_lo, PedersenOpening);
        _assertClass(opening_hi, PedersenOpening);
        const ret = wasm.batchedgroupedciphertext2handlesvalidityproofdata_new(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, grouped_ciphertext_lo.__wbg_ptr, grouped_ciphertext_hi.__wbg_ptr, amount_lo, amount_hi, opening_lo.__wbg_ptr, opening_hi.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return BatchedGroupedCiphertext2HandlesValidityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.batchedgroupedciphertext2handlesvalidityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {BatchedGroupedCiphertext2HandlesValidityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.batchedgroupedciphertext2handlesvalidityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return BatchedGroupedCiphertext2HandlesValidityProofData.__wrap(ret[0]);
    }
}
module.exports.BatchedGroupedCiphertext2HandlesValidityProofData = BatchedGroupedCiphertext2HandlesValidityProofData;

const BatchedGroupedCiphertext3HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_batchedgroupedciphertext3handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * Batched grouped ciphertext validity proof with two handles.
 */
class BatchedGroupedCiphertext3HandlesValidityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        BatchedGroupedCiphertext3HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_batchedgroupedciphertext3handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.BatchedGroupedCiphertext3HandlesValidityProof = BatchedGroupedCiphertext3HandlesValidityProof;

const BatchedGroupedCiphertext3HandlesValidityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_batchedgroupedciphertext3handlesvalidityproofcontext_free(ptr >>> 0, 1));

class BatchedGroupedCiphertext3HandlesValidityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(BatchedGroupedCiphertext3HandlesValidityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        BatchedGroupedCiphertext3HandlesValidityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        BatchedGroupedCiphertext3HandlesValidityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_batchedgroupedciphertext3handlesvalidityproofcontext_free(ptr, 0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get first_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set first_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get second_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set second_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get third_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set third_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedElGamalCiphertext3Handles}
     */
    get grouped_ciphertext_lo() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr);
        return PodGroupedElGamalCiphertext3Handles.__wrap(ret);
    }
    /**
     * @param {PodGroupedElGamalCiphertext3Handles} arg0
     */
    set grouped_ciphertext_lo(arg0) {
        _assertClass(arg0, PodGroupedElGamalCiphertext3Handles);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedElGamalCiphertext3Handles}
     */
    get grouped_ciphertext_hi() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_hi(this.__wbg_ptr);
        return PodGroupedElGamalCiphertext3Handles.__wrap(ret);
    }
    /**
     * @param {PodGroupedElGamalCiphertext3Handles} arg0
     */
    set grouped_ciphertext_hi(arg0) {
        _assertClass(arg0, PodGroupedElGamalCiphertext3Handles);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_hi(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.batchedgroupedciphertext3handlesvalidityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {BatchedGroupedCiphertext3HandlesValidityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.batchedgroupedciphertext3handlesvalidityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return BatchedGroupedCiphertext3HandlesValidityProofContext.__wrap(ret[0]);
    }
}
module.exports.BatchedGroupedCiphertext3HandlesValidityProofContext = BatchedGroupedCiphertext3HandlesValidityProofContext;

const BatchedGroupedCiphertext3HandlesValidityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_batchedgroupedciphertext3handlesvalidityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyBatchedGroupedCiphertext3HandlesValidity` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class BatchedGroupedCiphertext3HandlesValidityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(BatchedGroupedCiphertext3HandlesValidityProofData.prototype);
        obj.__wbg_ptr = ptr;
        BatchedGroupedCiphertext3HandlesValidityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        BatchedGroupedCiphertext3HandlesValidityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_batchedgroupedciphertext3handlesvalidityproofdata_free(ptr, 0);
    }
    /**
     * @returns {BatchedGroupedCiphertext3HandlesValidityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofdata_context(this.__wbg_ptr);
        return BatchedGroupedCiphertext3HandlesValidityProofContext.__wrap(ret);
    }
    /**
     * @param {BatchedGroupedCiphertext3HandlesValidityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, BatchedGroupedCiphertext3HandlesValidityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodBatchedGroupedCiphertext3HandlesValidityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofdata_proof(this.__wbg_ptr);
        return PodBatchedGroupedCiphertext3HandlesValidityProof.__wrap(ret);
    }
    /**
     * @param {PodBatchedGroupedCiphertext3HandlesValidityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodBatchedGroupedCiphertext3HandlesValidityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {ElGamalPubkey} third_pubkey
     * @param {GroupedElGamalCiphertext3Handles} grouped_ciphertext_lo
     * @param {GroupedElGamalCiphertext3Handles} grouped_ciphertext_hi
     * @param {bigint} amount_lo
     * @param {bigint} amount_hi
     * @param {PedersenOpening} opening_lo
     * @param {PedersenOpening} opening_hi
     * @returns {BatchedGroupedCiphertext3HandlesValidityProofData}
     */
    static new(first_pubkey, second_pubkey, third_pubkey, grouped_ciphertext_lo, grouped_ciphertext_hi, amount_lo, amount_hi, opening_lo, opening_hi) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(third_pubkey, ElGamalPubkey);
        _assertClass(grouped_ciphertext_lo, GroupedElGamalCiphertext3Handles);
        _assertClass(grouped_ciphertext_hi, GroupedElGamalCiphertext3Handles);
        _assertClass(opening_lo, PedersenOpening);
        _assertClass(opening_hi, PedersenOpening);
        const ret = wasm.batchedgroupedciphertext3handlesvalidityproofdata_new(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, third_pubkey.__wbg_ptr, grouped_ciphertext_lo.__wbg_ptr, grouped_ciphertext_hi.__wbg_ptr, amount_lo, amount_hi, opening_lo.__wbg_ptr, opening_hi.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return BatchedGroupedCiphertext3HandlesValidityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.batchedgroupedciphertext3handlesvalidityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {BatchedGroupedCiphertext3HandlesValidityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.batchedgroupedciphertext3handlesvalidityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return BatchedGroupedCiphertext3HandlesValidityProofData.__wrap(ret[0]);
    }
}
module.exports.BatchedGroupedCiphertext3HandlesValidityProofData = BatchedGroupedCiphertext3HandlesValidityProofData;

const CiphertextCiphertextEqualityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_ciphertextciphertextequalityproof_free(ptr >>> 0, 1));
/**
 * The ciphertext-ciphertext equality proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
class CiphertextCiphertextEqualityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        CiphertextCiphertextEqualityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_ciphertextciphertextequalityproof_free(ptr, 0);
    }
}
module.exports.CiphertextCiphertextEqualityProof = CiphertextCiphertextEqualityProof;

const CiphertextCiphertextEqualityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_ciphertextciphertextequalityproofcontext_free(ptr >>> 0, 1));
/**
 * The context data needed to verify a ciphertext-ciphertext equality proof.
 */
class CiphertextCiphertextEqualityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(CiphertextCiphertextEqualityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        CiphertextCiphertextEqualityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        CiphertextCiphertextEqualityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_ciphertextciphertextequalityproofcontext_free(ptr, 0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get first_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set first_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get second_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set second_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalCiphertext}
     */
    get first_ciphertext() {
        const ret = wasm.__wbg_get_ciphertextciphertextequalityproofcontext_first_ciphertext(this.__wbg_ptr);
        return PodElGamalCiphertext.__wrap(ret);
    }
    /**
     * @param {PodElGamalCiphertext} arg0
     */
    set first_ciphertext(arg0) {
        _assertClass(arg0, PodElGamalCiphertext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextciphertextequalityproofcontext_first_ciphertext(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalCiphertext}
     */
    get second_ciphertext() {
        const ret = wasm.__wbg_get_ciphertextciphertextequalityproofcontext_second_ciphertext(this.__wbg_ptr);
        return PodElGamalCiphertext.__wrap(ret);
    }
    /**
     * @param {PodElGamalCiphertext} arg0
     */
    set second_ciphertext(arg0) {
        _assertClass(arg0, PodElGamalCiphertext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextciphertextequalityproofcontext_second_ciphertext(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.ciphertextciphertextequalityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {CiphertextCiphertextEqualityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.ciphertextciphertextequalityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return CiphertextCiphertextEqualityProofContext.__wrap(ret[0]);
    }
}
module.exports.CiphertextCiphertextEqualityProofContext = CiphertextCiphertextEqualityProofContext;

const CiphertextCiphertextEqualityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_ciphertextciphertextequalityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyCiphertextCiphertextEquality` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class CiphertextCiphertextEqualityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(CiphertextCiphertextEqualityProofData.prototype);
        obj.__wbg_ptr = ptr;
        CiphertextCiphertextEqualityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        CiphertextCiphertextEqualityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_ciphertextciphertextequalityproofdata_free(ptr, 0);
    }
    /**
     * @returns {CiphertextCiphertextEqualityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_ciphertextciphertextequalityproofdata_context(this.__wbg_ptr);
        return CiphertextCiphertextEqualityProofContext.__wrap(ret);
    }
    /**
     * @param {CiphertextCiphertextEqualityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, CiphertextCiphertextEqualityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextciphertextequalityproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodCiphertextCiphertextEqualityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_ciphertextciphertextequalityproofdata_proof(this.__wbg_ptr);
        return PodCiphertextCiphertextEqualityProof.__wrap(ret);
    }
    /**
     * @param {PodCiphertextCiphertextEqualityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodCiphertextCiphertextEqualityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextciphertextequalityproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalKeypair} first_keypair
     * @param {ElGamalPubkey} second_pubkey
     * @param {ElGamalCiphertext} first_ciphertext
     * @param {ElGamalCiphertext} second_ciphertext
     * @param {PedersenOpening} second_opening
     * @param {bigint} amount
     * @returns {CiphertextCiphertextEqualityProofData}
     */
    static new(first_keypair, second_pubkey, first_ciphertext, second_ciphertext, second_opening, amount) {
        _assertClass(first_keypair, ElGamalKeypair);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(first_ciphertext, ElGamalCiphertext);
        _assertClass(second_ciphertext, ElGamalCiphertext);
        _assertClass(second_opening, PedersenOpening);
        const ret = wasm.ciphertextciphertextequalityproofdata_new(first_keypair.__wbg_ptr, second_pubkey.__wbg_ptr, first_ciphertext.__wbg_ptr, second_ciphertext.__wbg_ptr, second_opening.__wbg_ptr, amount);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return CiphertextCiphertextEqualityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.ciphertextciphertextequalityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {CiphertextCiphertextEqualityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.ciphertextciphertextequalityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return CiphertextCiphertextEqualityProofData.__wrap(ret[0]);
    }
}
module.exports.CiphertextCiphertextEqualityProofData = CiphertextCiphertextEqualityProofData;

const CiphertextCommitmentEqualityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_ciphertextcommitmentequalityproof_free(ptr >>> 0, 1));
/**
 * Equality proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
class CiphertextCommitmentEqualityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        CiphertextCommitmentEqualityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_ciphertextcommitmentequalityproof_free(ptr, 0);
    }
}
module.exports.CiphertextCommitmentEqualityProof = CiphertextCommitmentEqualityProof;

const CiphertextCommitmentEqualityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_ciphertextcommitmentequalityproofcontext_free(ptr >>> 0, 1));
/**
 * The context data needed to verify a ciphertext-commitment equality proof.
 */
class CiphertextCommitmentEqualityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(CiphertextCommitmentEqualityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        CiphertextCommitmentEqualityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        CiphertextCommitmentEqualityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_ciphertextcommitmentequalityproofcontext_free(ptr, 0);
    }
    /**
     * The ElGamal pubkey
     * @returns {PodElGamalPubkey}
     */
    get pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * The ElGamal pubkey
     * @param {PodElGamalPubkey} arg0
     */
    set pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * The ciphertext encrypted under the ElGamal pubkey
     * @returns {PodElGamalCiphertext}
     */
    get ciphertext() {
        const ret = wasm.__wbg_get_ciphertextcommitmentequalityproofcontext_ciphertext(this.__wbg_ptr);
        return PodElGamalCiphertext.__wrap(ret);
    }
    /**
     * The ciphertext encrypted under the ElGamal pubkey
     * @param {PodElGamalCiphertext} arg0
     */
    set ciphertext(arg0) {
        _assertClass(arg0, PodElGamalCiphertext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextcommitmentequalityproofcontext_ciphertext(this.__wbg_ptr, ptr0);
    }
    /**
     * The Pedersen commitment
     * @returns {PodPedersenCommitment}
     */
    get commitment() {
        const ret = wasm.__wbg_get_ciphertextcommitmentequalityproofcontext_commitment(this.__wbg_ptr);
        return PodPedersenCommitment.__wrap(ret);
    }
    /**
     * The Pedersen commitment
     * @param {PodPedersenCommitment} arg0
     */
    set commitment(arg0) {
        _assertClass(arg0, PodPedersenCommitment);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextcommitmentequalityproofcontext_commitment(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.ciphertextcommitmentequalityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {CiphertextCommitmentEqualityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.ciphertextcommitmentequalityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return CiphertextCommitmentEqualityProofContext.__wrap(ret[0]);
    }
}
module.exports.CiphertextCommitmentEqualityProofContext = CiphertextCommitmentEqualityProofContext;

const CiphertextCommitmentEqualityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_ciphertextcommitmentequalityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyCiphertextCommitmentEquality` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class CiphertextCommitmentEqualityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(CiphertextCommitmentEqualityProofData.prototype);
        obj.__wbg_ptr = ptr;
        CiphertextCommitmentEqualityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        CiphertextCommitmentEqualityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_ciphertextcommitmentequalityproofdata_free(ptr, 0);
    }
    /**
     * @returns {CiphertextCommitmentEqualityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_ciphertextcommitmentequalityproofdata_context(this.__wbg_ptr);
        return CiphertextCommitmentEqualityProofContext.__wrap(ret);
    }
    /**
     * @param {CiphertextCommitmentEqualityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, CiphertextCommitmentEqualityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextcommitmentequalityproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodCiphertextCommitmentEqualityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_ciphertextcommitmentequalityproofdata_proof(this.__wbg_ptr);
        return PodCiphertextCommitmentEqualityProof.__wrap(ret);
    }
    /**
     * @param {PodCiphertextCommitmentEqualityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodCiphertextCommitmentEqualityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextcommitmentequalityproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalKeypair} keypair
     * @param {ElGamalCiphertext} ciphertext
     * @param {PedersenCommitment} commitment
     * @param {PedersenOpening} opening
     * @param {bigint} amount
     * @returns {CiphertextCommitmentEqualityProofData}
     */
    static new(keypair, ciphertext, commitment, opening, amount) {
        _assertClass(keypair, ElGamalKeypair);
        _assertClass(ciphertext, ElGamalCiphertext);
        _assertClass(commitment, PedersenCommitment);
        _assertClass(opening, PedersenOpening);
        const ret = wasm.ciphertextcommitmentequalityproofdata_new(keypair.__wbg_ptr, ciphertext.__wbg_ptr, commitment.__wbg_ptr, opening.__wbg_ptr, amount);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return CiphertextCommitmentEqualityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.ciphertextcommitmentequalityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {CiphertextCommitmentEqualityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.ciphertextcommitmentequalityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return CiphertextCommitmentEqualityProofData.__wrap(ret[0]);
    }
}
module.exports.CiphertextCommitmentEqualityProofData = CiphertextCommitmentEqualityProofData;

const DecryptHandleFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_decrypthandle_free(ptr >>> 0, 1));
/**
 * Decryption handle for Pedersen commitment.
 */
class DecryptHandle {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(DecryptHandle.prototype);
        obj.__wbg_ptr = ptr;
        DecryptHandleFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        DecryptHandleFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_decrypthandle_free(ptr, 0);
    }
}
module.exports.DecryptHandle = DecryptHandle;

const ElGamalCiphertextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_elgamalciphertext_free(ptr >>> 0, 1));
/**
 * Ciphertext for the ElGamal encryption scheme.
 */
class ElGamalCiphertext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(ElGamalCiphertext.prototype);
        obj.__wbg_ptr = ptr;
        ElGamalCiphertextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        ElGamalCiphertextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_elgamalciphertext_free(ptr, 0);
    }
    /**
     * @returns {PedersenCommitment}
     */
    get commitment() {
        const ret = wasm.__wbg_get_elgamalciphertext_commitment(this.__wbg_ptr);
        return PedersenCommitment.__wrap(ret);
    }
    /**
     * @param {PedersenCommitment} arg0
     */
    set commitment(arg0) {
        _assertClass(arg0, PedersenCommitment);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_elgamalciphertext_commitment(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {DecryptHandle}
     */
    get handle() {
        const ret = wasm.__wbg_get_elgamalciphertext_handle(this.__wbg_ptr);
        return DecryptHandle.__wrap(ret);
    }
    /**
     * @param {DecryptHandle} arg0
     */
    set handle(arg0) {
        _assertClass(arg0, DecryptHandle);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_elgamalciphertext_handle(this.__wbg_ptr, ptr0);
    }
}
module.exports.ElGamalCiphertext = ElGamalCiphertext;

const ElGamalKeypairFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_elgamalkeypair_free(ptr >>> 0, 1));
/**
 * A (twisted) ElGamal encryption keypair.
 *
 * The instances of the secret key are zeroized on drop.
 */
class ElGamalKeypair {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(ElGamalKeypair.prototype);
        obj.__wbg_ptr = ptr;
        ElGamalKeypairFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        ElGamalKeypairFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_elgamalkeypair_free(ptr, 0);
    }
    /**
     * Generates the public and secret keys for ElGamal encryption.
     *
     * This function is randomized. It internally samples a scalar element using `OsRng`.
     * @returns {ElGamalKeypair}
     */
    static newRand() {
        const ret = wasm.elgamalkeypair_newRand();
        return ElGamalKeypair.__wrap(ret);
    }
    /**
     * @returns {ElGamalPubkey}
     */
    pubkeyOwned() {
        const ret = wasm.elgamalkeypair_pubkeyOwned(this.__wbg_ptr);
        return ElGamalPubkey.__wrap(ret);
    }
}
module.exports.ElGamalKeypair = ElGamalKeypair;

const ElGamalPubkeyFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_elgamalpubkey_free(ptr >>> 0, 1));
/**
 * Public key for the ElGamal encryption scheme.
 */
class ElGamalPubkey {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(ElGamalPubkey.prototype);
        obj.__wbg_ptr = ptr;
        ElGamalPubkeyFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        ElGamalPubkeyFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_elgamalpubkey_free(ptr, 0);
    }
    /**
     * @param {bigint} amount
     * @returns {ElGamalCiphertext}
     */
    encryptU64(amount) {
        const ret = wasm.elgamalpubkey_encryptU64(this.__wbg_ptr, amount);
        return ElGamalCiphertext.__wrap(ret);
    }
    /**
     * @param {bigint} amount
     * @param {PedersenOpening} opening
     * @returns {ElGamalCiphertext}
     */
    encryptWithU64(amount, opening) {
        _assertClass(opening, PedersenOpening);
        const ret = wasm.elgamalpubkey_encryptWithU64(this.__wbg_ptr, amount, opening.__wbg_ptr);
        return ElGamalCiphertext.__wrap(ret);
    }
}
module.exports.ElGamalPubkey = ElGamalPubkey;

const GroupedCiphertext2HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedciphertext2handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * The grouped ciphertext validity proof for 2 handles.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
class GroupedCiphertext2HandlesValidityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedCiphertext2HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedciphertext2handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.GroupedCiphertext2HandlesValidityProof = GroupedCiphertext2HandlesValidityProof;

const GroupedCiphertext2HandlesValidityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedciphertext2handlesvalidityproofcontext_free(ptr >>> 0, 1));

class GroupedCiphertext2HandlesValidityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(GroupedCiphertext2HandlesValidityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        GroupedCiphertext2HandlesValidityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedCiphertext2HandlesValidityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedciphertext2handlesvalidityproofcontext_free(ptr, 0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get first_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set first_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get second_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set second_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedElGamalCiphertext2Handles}
     */
    get grouped_ciphertext() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr);
        return PodGroupedElGamalCiphertext2Handles.__wrap(ret);
    }
    /**
     * @param {PodGroupedElGamalCiphertext2Handles} arg0
     */
    set grouped_ciphertext(arg0) {
        _assertClass(arg0, PodGroupedElGamalCiphertext2Handles);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.groupedciphertext2handlesvalidityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {GroupedCiphertext2HandlesValidityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.groupedciphertext2handlesvalidityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return GroupedCiphertext2HandlesValidityProofContext.__wrap(ret[0]);
    }
}
module.exports.GroupedCiphertext2HandlesValidityProofContext = GroupedCiphertext2HandlesValidityProofContext;

const GroupedCiphertext2HandlesValidityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedciphertext2handlesvalidityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the `ProofInstruction::VerifyGroupedCiphertextValidity`
 * instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class GroupedCiphertext2HandlesValidityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(GroupedCiphertext2HandlesValidityProofData.prototype);
        obj.__wbg_ptr = ptr;
        GroupedCiphertext2HandlesValidityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedCiphertext2HandlesValidityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedciphertext2handlesvalidityproofdata_free(ptr, 0);
    }
    /**
     * @returns {GroupedCiphertext2HandlesValidityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_groupedciphertext2handlesvalidityproofdata_context(this.__wbg_ptr);
        return GroupedCiphertext2HandlesValidityProofContext.__wrap(ret);
    }
    /**
     * @param {GroupedCiphertext2HandlesValidityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, GroupedCiphertext2HandlesValidityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_groupedciphertext2handlesvalidityproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedCiphertext2HandlesValidityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_groupedciphertext2handlesvalidityproofdata_proof(this.__wbg_ptr);
        return PodGroupedCiphertext2HandlesValidityProof.__wrap(ret);
    }
    /**
     * @param {PodGroupedCiphertext2HandlesValidityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodGroupedCiphertext2HandlesValidityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_groupedciphertext2handlesvalidityproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {GroupedElGamalCiphertext2Handles} grouped_ciphertext
     * @param {bigint} amount
     * @param {PedersenOpening} opening
     * @returns {GroupedCiphertext2HandlesValidityProofData}
     */
    static new(first_pubkey, second_pubkey, grouped_ciphertext, amount, opening) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(grouped_ciphertext, GroupedElGamalCiphertext2Handles);
        _assertClass(opening, PedersenOpening);
        const ret = wasm.groupedciphertext2handlesvalidityproofdata_new(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, grouped_ciphertext.__wbg_ptr, amount, opening.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return GroupedCiphertext2HandlesValidityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.groupedciphertext2handlesvalidityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {GroupedCiphertext2HandlesValidityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.groupedciphertext2handlesvalidityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return GroupedCiphertext2HandlesValidityProofData.__wrap(ret[0]);
    }
}
module.exports.GroupedCiphertext2HandlesValidityProofData = GroupedCiphertext2HandlesValidityProofData;

const GroupedCiphertext3HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedciphertext3handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * The grouped ciphertext validity proof for 3 handles.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
class GroupedCiphertext3HandlesValidityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedCiphertext3HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedciphertext3handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.GroupedCiphertext3HandlesValidityProof = GroupedCiphertext3HandlesValidityProof;

const GroupedCiphertext3HandlesValidityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedciphertext3handlesvalidityproofcontext_free(ptr >>> 0, 1));

class GroupedCiphertext3HandlesValidityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(GroupedCiphertext3HandlesValidityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        GroupedCiphertext3HandlesValidityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedCiphertext3HandlesValidityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedciphertext3handlesvalidityproofcontext_free(ptr, 0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get first_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set first_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get second_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set second_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    get third_pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {PodElGamalPubkey} arg0
     */
    set third_pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedElGamalCiphertext3Handles}
     */
    get grouped_ciphertext() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr);
        return PodGroupedElGamalCiphertext3Handles.__wrap(ret);
    }
    /**
     * @param {PodGroupedElGamalCiphertext3Handles} arg0
     */
    set grouped_ciphertext(arg0) {
        _assertClass(arg0, PodGroupedElGamalCiphertext3Handles);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_grouped_ciphertext_lo(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.groupedciphertext3handlesvalidityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {GroupedCiphertext3HandlesValidityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.groupedciphertext3handlesvalidityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return GroupedCiphertext3HandlesValidityProofContext.__wrap(ret[0]);
    }
}
module.exports.GroupedCiphertext3HandlesValidityProofContext = GroupedCiphertext3HandlesValidityProofContext;

const GroupedCiphertext3HandlesValidityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedciphertext3handlesvalidityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the
 * `ProofInstruction::VerifyGroupedCiphertext3HandlesValidity` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class GroupedCiphertext3HandlesValidityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(GroupedCiphertext3HandlesValidityProofData.prototype);
        obj.__wbg_ptr = ptr;
        GroupedCiphertext3HandlesValidityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedCiphertext3HandlesValidityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedciphertext3handlesvalidityproofdata_free(ptr, 0);
    }
    /**
     * @returns {GroupedCiphertext3HandlesValidityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_groupedciphertext3handlesvalidityproofdata_context(this.__wbg_ptr);
        return GroupedCiphertext3HandlesValidityProofContext.__wrap(ret);
    }
    /**
     * @param {GroupedCiphertext3HandlesValidityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, GroupedCiphertext3HandlesValidityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_groupedciphertext3handlesvalidityproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodGroupedCiphertext3HandlesValidityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_groupedciphertext3handlesvalidityproofdata_proof(this.__wbg_ptr);
        return PodGroupedCiphertext3HandlesValidityProof.__wrap(ret);
    }
    /**
     * @param {PodGroupedCiphertext3HandlesValidityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodGroupedCiphertext3HandlesValidityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_groupedciphertext3handlesvalidityproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {ElGamalPubkey} third_pubkey
     * @param {GroupedElGamalCiphertext3Handles} grouped_ciphertext
     * @param {bigint} amount
     * @param {PedersenOpening} opening
     * @returns {GroupedCiphertext3HandlesValidityProofData}
     */
    static new(first_pubkey, second_pubkey, third_pubkey, grouped_ciphertext, amount, opening) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(third_pubkey, ElGamalPubkey);
        _assertClass(grouped_ciphertext, GroupedElGamalCiphertext3Handles);
        _assertClass(opening, PedersenOpening);
        const ret = wasm.groupedciphertext3handlesvalidityproofdata_new(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, third_pubkey.__wbg_ptr, grouped_ciphertext.__wbg_ptr, amount, opening.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return GroupedCiphertext3HandlesValidityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.groupedciphertext3handlesvalidityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {GroupedCiphertext3HandlesValidityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.groupedciphertext3handlesvalidityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return GroupedCiphertext3HandlesValidityProofData.__wrap(ret[0]);
    }
}
module.exports.GroupedCiphertext3HandlesValidityProofData = GroupedCiphertext3HandlesValidityProofData;

const GroupedElGamalCiphertext2HandlesFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedelgamalciphertext2handles_free(ptr >>> 0, 1));

class GroupedElGamalCiphertext2Handles {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(GroupedElGamalCiphertext2Handles.prototype);
        obj.__wbg_ptr = ptr;
        GroupedElGamalCiphertext2HandlesFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedElGamalCiphertext2HandlesFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedelgamalciphertext2handles_free(ptr, 0);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {bigint} amount
     * @returns {GroupedElGamalCiphertext2Handles}
     */
    static encryptU64(first_pubkey, second_pubkey, amount) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        const ret = wasm.groupedelgamalciphertext2handles_encryptU64(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, amount);
        return GroupedElGamalCiphertext2Handles.__wrap(ret);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {bigint} amount
     * @param {PedersenOpening} opening
     * @returns {GroupedElGamalCiphertext2Handles}
     */
    static encryptionWithU64(first_pubkey, second_pubkey, amount, opening) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(opening, PedersenOpening);
        const ret = wasm.groupedelgamalciphertext2handles_encryptionWithU64(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, amount, opening.__wbg_ptr);
        return GroupedElGamalCiphertext2Handles.__wrap(ret);
    }
}
module.exports.GroupedElGamalCiphertext2Handles = GroupedElGamalCiphertext2Handles;

const GroupedElGamalCiphertext3HandlesFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_groupedelgamalciphertext3handles_free(ptr >>> 0, 1));

class GroupedElGamalCiphertext3Handles {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(GroupedElGamalCiphertext3Handles.prototype);
        obj.__wbg_ptr = ptr;
        GroupedElGamalCiphertext3HandlesFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        GroupedElGamalCiphertext3HandlesFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_groupedelgamalciphertext3handles_free(ptr, 0);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {ElGamalPubkey} third_pubkey
     * @param {bigint} amount
     * @returns {GroupedElGamalCiphertext3Handles}
     */
    static encryptU64(first_pubkey, second_pubkey, third_pubkey, amount) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(third_pubkey, ElGamalPubkey);
        const ret = wasm.groupedelgamalciphertext3handles_encryptU64(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, third_pubkey.__wbg_ptr, amount);
        return GroupedElGamalCiphertext3Handles.__wrap(ret);
    }
    /**
     * @param {ElGamalPubkey} first_pubkey
     * @param {ElGamalPubkey} second_pubkey
     * @param {ElGamalPubkey} third_pubkey
     * @param {bigint} amount
     * @param {PedersenOpening} opening
     * @returns {GroupedElGamalCiphertext3Handles}
     */
    static encryptionWithU64(first_pubkey, second_pubkey, third_pubkey, amount, opening) {
        _assertClass(first_pubkey, ElGamalPubkey);
        _assertClass(second_pubkey, ElGamalPubkey);
        _assertClass(third_pubkey, ElGamalPubkey);
        _assertClass(opening, PedersenOpening);
        const ret = wasm.groupedelgamalciphertext3handles_encryptionWithU64(first_pubkey.__wbg_ptr, second_pubkey.__wbg_ptr, third_pubkey.__wbg_ptr, amount, opening.__wbg_ptr);
        return GroupedElGamalCiphertext3Handles.__wrap(ret);
    }
}
module.exports.GroupedElGamalCiphertext3Handles = GroupedElGamalCiphertext3Handles;

const HashFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_hash_free(ptr >>> 0, 1));
/**
 * A hash; the 32-byte output of a hashing algorithm.
 *
 * This struct is used most often in `solana-sdk` and related crates to contain
 * a [SHA-256] hash, but may instead contain a [blake3] hash.
 *
 * [SHA-256]: https://en.wikipedia.org/wiki/SHA-2
 * [blake3]: https://github.com/BLAKE3-team/BLAKE3
 */
class Hash {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(Hash.prototype);
        obj.__wbg_ptr = ptr;
        HashFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        HashFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_hash_free(ptr, 0);
    }
    /**
     * Create a new Hash object
     *
     * * `value` - optional hash as a base58 encoded string, `Uint8Array`, `[number]`
     * @param {any} value
     */
    constructor(value) {
        const ret = wasm.hash_constructor(value);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        this.__wbg_ptr = ret[0] >>> 0;
        HashFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * Return the base58 string representation of the hash
     * @returns {string}
     */
    toString() {
        let deferred1_0;
        let deferred1_1;
        try {
            const ret = wasm.hash_toString(this.__wbg_ptr);
            deferred1_0 = ret[0];
            deferred1_1 = ret[1];
            return getStringFromWasm0(ret[0], ret[1]);
        } finally {
            wasm.__wbindgen_free(deferred1_0, deferred1_1, 1);
        }
    }
    /**
     * Checks if two `Hash`s are equal
     * @param {Hash} other
     * @returns {boolean}
     */
    equals(other) {
        _assertClass(other, Hash);
        const ret = wasm.hash_equals(this.__wbg_ptr, other.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * Return the `Uint8Array` representation of the hash
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.hash_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
}
module.exports.Hash = Hash;

const InstructionFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_instruction_free(ptr >>> 0, 1));
/**
 * wasm-bindgen version of the Instruction struct.
 * This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
 * is fixed. This must not diverge from the regular non-wasm Instruction struct.
 */
class Instruction {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(Instruction.prototype);
        obj.__wbg_ptr = ptr;
        InstructionFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        InstructionFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_instruction_free(ptr, 0);
    }
}
module.exports.Instruction = Instruction;

const InstructionsFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_instructions_free(ptr >>> 0, 1));

class Instructions {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        InstructionsFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_instructions_free(ptr, 0);
    }
    constructor() {
        const ret = wasm.instructions_constructor();
        this.__wbg_ptr = ret >>> 0;
        InstructionsFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * @param {Instruction} instruction
     */
    push(instruction) {
        _assertClass(instruction, Instruction);
        var ptr0 = instruction.__destroy_into_raw();
        wasm.instructions_push(this.__wbg_ptr, ptr0);
    }
}
module.exports.Instructions = Instructions;

const KeypairFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_keypair_free(ptr >>> 0, 1));
/**
 * A vanilla Ed25519 key pair
 */
class Keypair {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(Keypair.prototype);
        obj.__wbg_ptr = ptr;
        KeypairFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        KeypairFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_keypair_free(ptr, 0);
    }
    /**
     * Create a new `Keypair `
     */
    constructor() {
        const ret = wasm.keypair_constructor();
        this.__wbg_ptr = ret >>> 0;
        KeypairFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * Convert a `Keypair` to a `Uint8Array`
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.keypair_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * Recover a `Keypair` from a `Uint8Array`
     * @param {Uint8Array} bytes
     * @returns {Keypair}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.keypair_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return Keypair.__wrap(ret[0]);
    }
    /**
     * Return the `Pubkey` for this `Keypair`
     * @returns {Pubkey}
     */
    pubkey() {
        const ret = wasm.keypair_pubkey(this.__wbg_ptr);
        return Pubkey.__wrap(ret);
    }
}
module.exports.Keypair = Keypair;

const MessageFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_message_free(ptr >>> 0, 1));
/**
 * wasm-bindgen version of the Message struct.
 * This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
 * is fixed. This must not diverge from the regular non-wasm Message struct.
 */
class Message {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(Message.prototype);
        obj.__wbg_ptr = ptr;
        MessageFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        MessageFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_message_free(ptr, 0);
    }
    /**
     * The id of a recent ledger entry.
     * @returns {Hash}
     */
    get recent_blockhash() {
        const ret = wasm.__wbg_get_message_recent_blockhash(this.__wbg_ptr);
        return Hash.__wrap(ret);
    }
    /**
     * The id of a recent ledger entry.
     * @param {Hash} arg0
     */
    set recent_blockhash(arg0) {
        _assertClass(arg0, Hash);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_message_recent_blockhash(this.__wbg_ptr, ptr0);
    }
}
module.exports.Message = Message;

const PedersenFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pedersen_free(ptr >>> 0, 1));
/**
 * Algorithm handle for the Pedersen commitment scheme.
 */
class Pedersen {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PedersenFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pedersen_free(ptr, 0);
    }
    /**
     * @param {bigint} amount
     * @param {PedersenOpening} opening
     * @returns {PedersenCommitment}
     */
    static withU64(amount, opening) {
        _assertClass(opening, PedersenOpening);
        const ret = wasm.pedersen_withU64(amount, opening.__wbg_ptr);
        return PedersenCommitment.__wrap(ret);
    }
}
module.exports.Pedersen = Pedersen;

const PedersenCommitmentFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pedersencommitment_free(ptr >>> 0, 1));
/**
 * Pedersen commitment type.
 */
class PedersenCommitment {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PedersenCommitment.prototype);
        obj.__wbg_ptr = ptr;
        PedersenCommitmentFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PedersenCommitmentFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pedersencommitment_free(ptr, 0);
    }
}
module.exports.PedersenCommitment = PedersenCommitment;

const PedersenOpeningFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pedersenopening_free(ptr >>> 0, 1));
/**
 * Pedersen opening type.
 *
 * Instances of Pedersen openings are zeroized on drop.
 */
class PedersenOpening {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PedersenOpening.prototype);
        obj.__wbg_ptr = ptr;
        PedersenOpeningFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PedersenOpeningFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pedersenopening_free(ptr, 0);
    }
    /**
     * @returns {PedersenOpening}
     */
    static newRand() {
        const ret = wasm.pedersenopening_newRand();
        return PedersenOpening.__wrap(ret);
    }
}
module.exports.PedersenOpening = PedersenOpening;

const PercentageWithCapProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_percentagewithcapproof_free(ptr >>> 0, 1));
/**
 * Percentage-with-cap proof.
 *
 * The proof consists of two main components: `percentage_max_proof` and
 * `percentage_equality_proof`. If the committed amount is greater than the maximum cap value,
 * then the `percentage_max_proof` is properly generated and `percentage_equality_proof` is
 * simulated. If the encrypted amount is smaller than the maximum cap bound, the
 * `percentage_equality_proof` is properly generated and `percentage_max_proof` is simulated.
 */
class PercentageWithCapProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PercentageWithCapProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_percentagewithcapproof_free(ptr, 0);
    }
}
module.exports.PercentageWithCapProof = PercentageWithCapProof;

const PercentageWithCapProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_percentagewithcapproofcontext_free(ptr >>> 0, 1));
/**
 * The context data needed to verify a percentage-with-cap proof.
 *
 * We refer to [`ZK ElGamal proof`] for the formal details on how the percentage-with-cap proof is
 * computed.
 *
 * [`ZK ElGamal proof`]: https://docs.solanalabs.com/runtime/zk-token-proof
 */
class PercentageWithCapProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PercentageWithCapProofContext.prototype);
        obj.__wbg_ptr = ptr;
        PercentageWithCapProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PercentageWithCapProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_percentagewithcapproofcontext_free(ptr, 0);
    }
    /**
     * The Pedersen commitment to the percentage amount.
     * @returns {PodPedersenCommitment}
     */
    get percentage_commitment() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodPedersenCommitment.__wrap(ret);
    }
    /**
     * The Pedersen commitment to the percentage amount.
     * @param {PodPedersenCommitment} arg0
     */
    set percentage_commitment(arg0) {
        _assertClass(arg0, PodPedersenCommitment);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * The Pedersen commitment to the delta amount.
     * @returns {PodPedersenCommitment}
     */
    get delta_commitment() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr);
        return PodPedersenCommitment.__wrap(ret);
    }
    /**
     * The Pedersen commitment to the delta amount.
     * @param {PodPedersenCommitment} arg0
     */
    set delta_commitment(arg0) {
        _assertClass(arg0, PodPedersenCommitment);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_second_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * The Pedersen commitment to the claimed amount.
     * @returns {PodPedersenCommitment}
     */
    get claimed_commitment() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey(this.__wbg_ptr);
        return PodPedersenCommitment.__wrap(ret);
    }
    /**
     * The Pedersen commitment to the claimed amount.
     * @param {PodPedersenCommitment} arg0
     */
    set claimed_commitment(arg0) {
        _assertClass(arg0, PodPedersenCommitment);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext3handlesvalidityproofcontext_third_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * The maximum cap bound.
     * @returns {PodU64}
     */
    get max_value() {
        const ret = wasm.__wbg_get_percentagewithcapproofcontext_max_value(this.__wbg_ptr);
        return PodU64.__wrap(ret);
    }
    /**
     * The maximum cap bound.
     * @param {PodU64} arg0
     */
    set max_value(arg0) {
        _assertClass(arg0, PodU64);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_percentagewithcapproofcontext_max_value(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.percentagewithcapproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {PercentageWithCapProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.percentagewithcapproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return PercentageWithCapProofContext.__wrap(ret[0]);
    }
}
module.exports.PercentageWithCapProofContext = PercentageWithCapProofContext;

const PercentageWithCapProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_percentagewithcapproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the `ProofInstruction::VerifyPercentageWithCap`
 * instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class PercentageWithCapProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PercentageWithCapProofData.prototype);
        obj.__wbg_ptr = ptr;
        PercentageWithCapProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PercentageWithCapProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_percentagewithcapproofdata_free(ptr, 0);
    }
    /**
     * @returns {PercentageWithCapProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_percentagewithcapproofdata_context(this.__wbg_ptr);
        return PercentageWithCapProofContext.__wrap(ret);
    }
    /**
     * @param {PercentageWithCapProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, PercentageWithCapProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_percentagewithcapproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {PodPercentageWithCapProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_percentagewithcapproofdata_proof(this.__wbg_ptr);
        return PodPercentageWithCapProof.__wrap(ret);
    }
    /**
     * @param {PodPercentageWithCapProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodPercentageWithCapProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_percentagewithcapproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {PedersenCommitment} percentage_commitment
     * @param {PedersenOpening} percentage_opening
     * @param {bigint} percentage_amount
     * @param {PedersenCommitment} delta_commitment
     * @param {PedersenOpening} delta_opening
     * @param {bigint} delta_amount
     * @param {PedersenCommitment} claimed_commitment
     * @param {PedersenOpening} claimed_opening
     * @param {bigint} max_value
     * @returns {PercentageWithCapProofData}
     */
    static new(percentage_commitment, percentage_opening, percentage_amount, delta_commitment, delta_opening, delta_amount, claimed_commitment, claimed_opening, max_value) {
        _assertClass(percentage_commitment, PedersenCommitment);
        _assertClass(percentage_opening, PedersenOpening);
        _assertClass(delta_commitment, PedersenCommitment);
        _assertClass(delta_opening, PedersenOpening);
        _assertClass(claimed_commitment, PedersenCommitment);
        _assertClass(claimed_opening, PedersenOpening);
        const ret = wasm.percentagewithcapproofdata_new(percentage_commitment.__wbg_ptr, percentage_opening.__wbg_ptr, percentage_amount, delta_commitment.__wbg_ptr, delta_opening.__wbg_ptr, delta_amount, claimed_commitment.__wbg_ptr, claimed_opening.__wbg_ptr, max_value);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return PercentageWithCapProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.percentagewithcapproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {PercentageWithCapProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.percentagewithcapproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return PercentageWithCapProofData.__wrap(ret[0]);
    }
}
module.exports.PercentageWithCapProofData = PercentageWithCapProofData;

const PodAeCiphertextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podaeciphertext_free(ptr >>> 0, 1));
/**
 * The `AeCiphertext` type as a `Pod`.
 */
class PodAeCiphertext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodAeCiphertext.prototype);
        obj.__wbg_ptr = ptr;
        PodAeCiphertextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodAeCiphertextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podaeciphertext_free(ptr, 0);
    }
    /**
     * @param {any} value
     */
    constructor(value) {
        const ret = wasm.podaeciphertext_constructor(value);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        this.__wbg_ptr = ret[0] >>> 0;
        PodAeCiphertextFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * @returns {string}
     */
    toString() {
        let deferred1_0;
        let deferred1_1;
        try {
            const ret = wasm.podaeciphertext_toString(this.__wbg_ptr);
            deferred1_0 = ret[0];
            deferred1_1 = ret[1];
            return getStringFromWasm0(ret[0], ret[1]);
        } finally {
            wasm.__wbindgen_free(deferred1_0, deferred1_1, 1);
        }
    }
    /**
     * @param {PodAeCiphertext} other
     * @returns {boolean}
     */
    equals(other) {
        _assertClass(other, PodAeCiphertext);
        const ret = wasm.podaeciphertext_equals(this.__wbg_ptr, other.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.podaeciphertext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @returns {PodAeCiphertext}
     */
    static zeroed() {
        const ret = wasm.podaeciphertext_zeroed();
        return PodAeCiphertext.__wrap(ret);
    }
    /**
     * @param {AeCiphertext} decoded
     * @returns {PodAeCiphertext}
     */
    static encode(decoded) {
        _assertClass(decoded, AeCiphertext);
        const ret = wasm.podaeciphertext_encode(decoded.__wbg_ptr);
        return PodAeCiphertext.__wrap(ret);
    }
    /**
     * @returns {AeCiphertext}
     */
    decode() {
        const ret = wasm.podaeciphertext_decode(this.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return AeCiphertext.__wrap(ret[0]);
    }
}
module.exports.PodAeCiphertext = PodAeCiphertext;

const PodBatchedGroupedCiphertext2HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podbatchedgroupedciphertext2handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * The `BatchedGroupedCiphertext2HandlesValidityProof` type as a `Pod`.
 */
class PodBatchedGroupedCiphertext2HandlesValidityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodBatchedGroupedCiphertext2HandlesValidityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodBatchedGroupedCiphertext2HandlesValidityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodBatchedGroupedCiphertext2HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podbatchedgroupedciphertext2handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.PodBatchedGroupedCiphertext2HandlesValidityProof = PodBatchedGroupedCiphertext2HandlesValidityProof;

const PodBatchedGroupedCiphertext3HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podbatchedgroupedciphertext3handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * The `BatchedGroupedCiphertext3HandlesValidityProof` type as a `Pod`.
 */
class PodBatchedGroupedCiphertext3HandlesValidityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodBatchedGroupedCiphertext3HandlesValidityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodBatchedGroupedCiphertext3HandlesValidityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodBatchedGroupedCiphertext3HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podbatchedgroupedciphertext3handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.PodBatchedGroupedCiphertext3HandlesValidityProof = PodBatchedGroupedCiphertext3HandlesValidityProof;

const PodCiphertextCiphertextEqualityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podciphertextciphertextequalityproof_free(ptr >>> 0, 1));
/**
 * The `CiphertextCiphertextEqualityProof` type as a `Pod`.
 */
class PodCiphertextCiphertextEqualityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodCiphertextCiphertextEqualityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodCiphertextCiphertextEqualityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodCiphertextCiphertextEqualityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podciphertextciphertextequalityproof_free(ptr, 0);
    }
}
module.exports.PodCiphertextCiphertextEqualityProof = PodCiphertextCiphertextEqualityProof;

const PodCiphertextCommitmentEqualityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podciphertextcommitmentequalityproof_free(ptr >>> 0, 1));
/**
 * The `CiphertextCommitmentEqualityProof` type as a `Pod`.
 */
class PodCiphertextCommitmentEqualityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodCiphertextCommitmentEqualityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodCiphertextCommitmentEqualityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodCiphertextCommitmentEqualityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podciphertextcommitmentequalityproof_free(ptr, 0);
    }
}
module.exports.PodCiphertextCommitmentEqualityProof = PodCiphertextCommitmentEqualityProof;

const PodElGamalCiphertextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podelgamalciphertext_free(ptr >>> 0, 1));
/**
 * The `ElGamalCiphertext` type as a `Pod`.
 */
class PodElGamalCiphertext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodElGamalCiphertext.prototype);
        obj.__wbg_ptr = ptr;
        PodElGamalCiphertextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodElGamalCiphertextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podelgamalciphertext_free(ptr, 0);
    }
    /**
     * @param {any} value
     */
    constructor(value) {
        const ret = wasm.podelgamalciphertext_constructor(value);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        this.__wbg_ptr = ret[0] >>> 0;
        PodElGamalCiphertextFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * @returns {string}
     */
    toString() {
        let deferred1_0;
        let deferred1_1;
        try {
            const ret = wasm.podelgamalciphertext_toString(this.__wbg_ptr);
            deferred1_0 = ret[0];
            deferred1_1 = ret[1];
            return getStringFromWasm0(ret[0], ret[1]);
        } finally {
            wasm.__wbindgen_free(deferred1_0, deferred1_1, 1);
        }
    }
    /**
     * @param {PodElGamalCiphertext} other
     * @returns {boolean}
     */
    equals(other) {
        _assertClass(other, PodElGamalCiphertext);
        const ret = wasm.podelgamalciphertext_equals(this.__wbg_ptr, other.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.podelgamalciphertext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @returns {PodElGamalCiphertext}
     */
    static zeroed() {
        const ret = wasm.podaeciphertext_zeroed();
        return PodElGamalCiphertext.__wrap(ret);
    }
    /**
     * @param {ElGamalCiphertext} decoded
     * @returns {PodElGamalCiphertext}
     */
    static encode(decoded) {
        _assertClass(decoded, ElGamalCiphertext);
        const ret = wasm.podelgamalciphertext_encode(decoded.__wbg_ptr);
        return PodElGamalCiphertext.__wrap(ret);
    }
    /**
     * @returns {ElGamalCiphertext}
     */
    decode() {
        const ret = wasm.podelgamalciphertext_decode(this.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return ElGamalCiphertext.__wrap(ret[0]);
    }
}
module.exports.PodElGamalCiphertext = PodElGamalCiphertext;

const PodElGamalPubkeyFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podelgamalpubkey_free(ptr >>> 0, 1));
/**
 * The `ElGamalPubkey` type as a `Pod`.
 */
class PodElGamalPubkey {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodElGamalPubkey.prototype);
        obj.__wbg_ptr = ptr;
        PodElGamalPubkeyFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodElGamalPubkeyFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podelgamalpubkey_free(ptr, 0);
    }
    /**
     * @param {any} value
     */
    constructor(value) {
        const ret = wasm.podelgamalpubkey_constructor(value);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        this.__wbg_ptr = ret[0] >>> 0;
        PodElGamalPubkeyFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * @returns {string}
     */
    toString() {
        let deferred1_0;
        let deferred1_1;
        try {
            const ret = wasm.podelgamalpubkey_toString(this.__wbg_ptr);
            deferred1_0 = ret[0];
            deferred1_1 = ret[1];
            return getStringFromWasm0(ret[0], ret[1]);
        } finally {
            wasm.__wbindgen_free(deferred1_0, deferred1_1, 1);
        }
    }
    /**
     * @param {PodElGamalPubkey} other
     * @returns {boolean}
     */
    equals(other) {
        _assertClass(other, PodElGamalPubkey);
        const ret = wasm.hash_equals(this.__wbg_ptr, other.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.podelgamalpubkey_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @returns {PodElGamalPubkey}
     */
    static zeroed() {
        const ret = wasm.podelgamalpubkey_zeroed();
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @param {ElGamalPubkey} decoded
     * @returns {PodElGamalPubkey}
     */
    static encode(decoded) {
        _assertClass(decoded, ElGamalPubkey);
        const ret = wasm.podelgamalpubkey_encode(decoded.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * @returns {ElGamalPubkey}
     */
    decode() {
        const ret = wasm.podelgamalpubkey_decode(this.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return ElGamalPubkey.__wrap(ret[0]);
    }
}
module.exports.PodElGamalPubkey = PodElGamalPubkey;

const PodGroupedCiphertext2HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podgroupedciphertext2handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * The `GroupedCiphertext2HandlesValidityProof` type as a `Pod`.
 */
class PodGroupedCiphertext2HandlesValidityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodGroupedCiphertext2HandlesValidityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodGroupedCiphertext2HandlesValidityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodGroupedCiphertext2HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podgroupedciphertext2handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.PodGroupedCiphertext2HandlesValidityProof = PodGroupedCiphertext2HandlesValidityProof;

const PodGroupedCiphertext3HandlesValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podgroupedciphertext3handlesvalidityproof_free(ptr >>> 0, 1));
/**
 * The `GroupedCiphertext3HandlesValidityProof` type as a `Pod`.
 */
class PodGroupedCiphertext3HandlesValidityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodGroupedCiphertext3HandlesValidityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodGroupedCiphertext3HandlesValidityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodGroupedCiphertext3HandlesValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podgroupedciphertext3handlesvalidityproof_free(ptr, 0);
    }
}
module.exports.PodGroupedCiphertext3HandlesValidityProof = PodGroupedCiphertext3HandlesValidityProof;

const PodGroupedElGamalCiphertext2HandlesFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podgroupedelgamalciphertext2handles_free(ptr >>> 0, 1));
/**
 * The `GroupedElGamalCiphertext` type with two decryption handles as a `Pod`
 */
class PodGroupedElGamalCiphertext2Handles {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodGroupedElGamalCiphertext2Handles.prototype);
        obj.__wbg_ptr = ptr;
        PodGroupedElGamalCiphertext2HandlesFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodGroupedElGamalCiphertext2HandlesFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podgroupedelgamalciphertext2handles_free(ptr, 0);
    }
}
module.exports.PodGroupedElGamalCiphertext2Handles = PodGroupedElGamalCiphertext2Handles;

const PodGroupedElGamalCiphertext3HandlesFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podgroupedelgamalciphertext3handles_free(ptr >>> 0, 1));
/**
 * The `GroupedElGamalCiphertext` type with three decryption handles as a `Pod`
 */
class PodGroupedElGamalCiphertext3Handles {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodGroupedElGamalCiphertext3Handles.prototype);
        obj.__wbg_ptr = ptr;
        PodGroupedElGamalCiphertext3HandlesFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodGroupedElGamalCiphertext3HandlesFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podgroupedelgamalciphertext3handles_free(ptr, 0);
    }
}
module.exports.PodGroupedElGamalCiphertext3Handles = PodGroupedElGamalCiphertext3Handles;

const PodPedersenCommitmentFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podpedersencommitment_free(ptr >>> 0, 1));
/**
 * The `PedersenCommitment` type as a `Pod`.
 */
class PodPedersenCommitment {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodPedersenCommitment.prototype);
        obj.__wbg_ptr = ptr;
        PodPedersenCommitmentFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodPedersenCommitmentFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podpedersencommitment_free(ptr, 0);
    }
}
module.exports.PodPedersenCommitment = PodPedersenCommitment;

const PodPercentageWithCapProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podpercentagewithcapproof_free(ptr >>> 0, 1));
/**
 * The `PercentageWithCapProof` type as a `Pod`.
 */
class PodPercentageWithCapProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodPercentageWithCapProof.prototype);
        obj.__wbg_ptr = ptr;
        PodPercentageWithCapProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodPercentageWithCapProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podpercentagewithcapproof_free(ptr, 0);
    }
}
module.exports.PodPercentageWithCapProof = PodPercentageWithCapProof;

const PodPubkeyValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podpubkeyvalidityproof_free(ptr >>> 0, 1));
/**
 * The `PubkeyValidityProof` type as a `Pod`.
 */
class PodPubkeyValidityProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodPubkeyValidityProof.prototype);
        obj.__wbg_ptr = ptr;
        PodPubkeyValidityProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodPubkeyValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podpubkeyvalidityproof_free(ptr, 0);
    }
}
module.exports.PodPubkeyValidityProof = PodPubkeyValidityProof;

const PodU64Finalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podu64_free(ptr >>> 0, 1));

class PodU64 {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodU64.prototype);
        obj.__wbg_ptr = ptr;
        PodU64Finalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodU64Finalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podu64_free(ptr, 0);
    }
}
module.exports.PodU64 = PodU64;

const PodZeroCiphertextProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_podzerociphertextproof_free(ptr >>> 0, 1));
/**
 * The `ZeroCiphertextProof` type as a `Pod`.
 */
class PodZeroCiphertextProof {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PodZeroCiphertextProof.prototype);
        obj.__wbg_ptr = ptr;
        PodZeroCiphertextProofFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PodZeroCiphertextProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_podzerociphertextproof_free(ptr, 0);
    }
}
module.exports.PodZeroCiphertextProof = PodZeroCiphertextProof;

const PubkeyFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pubkey_free(ptr >>> 0, 1));
/**
 * The address of a [Solana account][acc].
 *
 * Some account addresses are [ed25519] public keys, with corresponding secret
 * keys that are managed off-chain. Often, though, account addresses do not
 * have corresponding secret keys &mdash; as with [_program derived
 * addresses_][pdas] &mdash; or the secret key is not relevant to the operation
 * of a program, and may have even been disposed of. As running Solana programs
 * can not safely create or manage secret keys, the full [`Keypair`] is not
 * defined in `solana-program` but in `solana-sdk`.
 *
 * [acc]: https://solana.com/docs/core/accounts
 * [ed25519]: https://ed25519.cr.yp.to/
 * [pdas]: https://solana.com/docs/core/cpi#program-derived-addresses
 * [`Keypair`]: https://docs.rs/solana-sdk/latest/solana_sdk/signer/keypair/struct.Keypair.html
 */
class Pubkey {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(Pubkey.prototype);
        obj.__wbg_ptr = ptr;
        PubkeyFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PubkeyFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pubkey_free(ptr, 0);
    }
    /**
     * Create a new Pubkey object
     *
     * * `value` - optional public key as a base58 encoded string, `Uint8Array`, `[number]`
     * @param {any} value
     */
    constructor(value) {
        const ret = wasm.pubkey_constructor(value);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        this.__wbg_ptr = ret[0] >>> 0;
        PubkeyFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * Return the base58 string representation of the public key
     * @returns {string}
     */
    toString() {
        let deferred1_0;
        let deferred1_1;
        try {
            const ret = wasm.pubkey_toString(this.__wbg_ptr);
            deferred1_0 = ret[0];
            deferred1_1 = ret[1];
            return getStringFromWasm0(ret[0], ret[1]);
        } finally {
            wasm.__wbindgen_free(deferred1_0, deferred1_1, 1);
        }
    }
    /**
     * Check if a `Pubkey` is on the ed25519 curve.
     * @returns {boolean}
     */
    isOnCurve() {
        const ret = wasm.pubkey_isOnCurve(this.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * Checks if two `Pubkey`s are equal
     * @param {Pubkey} other
     * @returns {boolean}
     */
    equals(other) {
        _assertClass(other, Pubkey);
        const ret = wasm.hash_equals(this.__wbg_ptr, other.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * Return the `Uint8Array` representation of the public key
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.pubkey_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * Derive a Pubkey from another Pubkey, string seed, and a program id
     * @param {Pubkey} base
     * @param {string} seed
     * @param {Pubkey} owner
     * @returns {Pubkey}
     */
    static createWithSeed(base, seed, owner) {
        _assertClass(base, Pubkey);
        const ptr0 = passStringToWasm0(seed, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(owner, Pubkey);
        const ret = wasm.pubkey_createWithSeed(base.__wbg_ptr, ptr0, len0, owner.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return Pubkey.__wrap(ret[0]);
    }
    /**
     * Derive a program address from seeds and a program id
     * @param {any[]} seeds
     * @param {Pubkey} program_id
     * @returns {Pubkey}
     */
    static createProgramAddress(seeds, program_id) {
        const ptr0 = passArrayJsValueToWasm0(seeds, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(program_id, Pubkey);
        const ret = wasm.pubkey_createProgramAddress(ptr0, len0, program_id.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return Pubkey.__wrap(ret[0]);
    }
    /**
     * Find a valid program address
     *
     * Returns:
     * * `[PubKey, number]` - the program address and bump seed
     * @param {any[]} seeds
     * @param {Pubkey} program_id
     * @returns {any}
     */
    static findProgramAddress(seeds, program_id) {
        const ptr0 = passArrayJsValueToWasm0(seeds, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(program_id, Pubkey);
        const ret = wasm.pubkey_findProgramAddress(ptr0, len0, program_id.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return takeFromExternrefTable0(ret[0]);
    }
}
module.exports.Pubkey = Pubkey;

const PubkeyValidityProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pubkeyvalidityproof_free(ptr >>> 0, 1));
/**
 * Public-key proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
class PubkeyValidityProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PubkeyValidityProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pubkeyvalidityproof_free(ptr, 0);
    }
}
module.exports.PubkeyValidityProof = PubkeyValidityProof;

const PubkeyValidityProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pubkeyvalidityproofcontext_free(ptr >>> 0, 1));
/**
 * The context data needed to verify a pubkey validity proof.
 */
class PubkeyValidityProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PubkeyValidityProofContext.prototype);
        obj.__wbg_ptr = ptr;
        PubkeyValidityProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PubkeyValidityProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pubkeyvalidityproofcontext_free(ptr, 0);
    }
    /**
     * The public key to be proved
     * @returns {PodElGamalPubkey}
     */
    get pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * The public key to be proved
     * @param {PodElGamalPubkey} arg0
     */
    set pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.pubkeyvalidityproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {PubkeyValidityProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.pubkeyvalidityproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return PubkeyValidityProofContext.__wrap(ret[0]);
    }
}
module.exports.PubkeyValidityProofContext = PubkeyValidityProofContext;

const PubkeyValidityProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_pubkeyvalidityproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the `ProofInstruction::VerifyPubkeyValidity`
 * instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class PubkeyValidityProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(PubkeyValidityProofData.prototype);
        obj.__wbg_ptr = ptr;
        PubkeyValidityProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        PubkeyValidityProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_pubkeyvalidityproofdata_free(ptr, 0);
    }
    /**
     * The context data for the public key validity proof
     * @returns {PubkeyValidityProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PubkeyValidityProofContext.__wrap(ret);
    }
    /**
     * The context data for the public key validity proof
     * @param {PubkeyValidityProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, PubkeyValidityProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * Proof that the public key is well-formed
     * @returns {PodPubkeyValidityProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_ciphertextcommitmentequalityproofcontext_ciphertext(this.__wbg_ptr);
        return PodPubkeyValidityProof.__wrap(ret);
    }
    /**
     * Proof that the public key is well-formed
     * @param {PodPubkeyValidityProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodPubkeyValidityProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextcommitmentequalityproofcontext_ciphertext(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalKeypair} keypair
     * @returns {PubkeyValidityProofData}
     */
    static new(keypair) {
        _assertClass(keypair, ElGamalKeypair);
        const ret = wasm.pubkeyvalidityproofdata_new(keypair.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return PubkeyValidityProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.pubkeyvalidityproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {PubkeyValidityProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.pubkeyvalidityproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return PubkeyValidityProofData.__wrap(ret[0]);
    }
}
module.exports.PubkeyValidityProofData = PubkeyValidityProofData;

const SystemInstructionFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_systeminstruction_free(ptr >>> 0, 1));

class SystemInstruction {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        SystemInstructionFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_systeminstruction_free(ptr, 0);
    }
    /**
     * @param {Pubkey} from_pubkey
     * @param {Pubkey} to_pubkey
     * @param {bigint} lamports
     * @param {bigint} space
     * @param {Pubkey} owner
     * @returns {Instruction}
     */
    static createAccount(from_pubkey, to_pubkey, lamports, space, owner) {
        _assertClass(from_pubkey, Pubkey);
        _assertClass(to_pubkey, Pubkey);
        _assertClass(owner, Pubkey);
        const ret = wasm.systeminstruction_createAccount(from_pubkey.__wbg_ptr, to_pubkey.__wbg_ptr, lamports, space, owner.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} from_pubkey
     * @param {Pubkey} to_pubkey
     * @param {Pubkey} base
     * @param {string} seed
     * @param {bigint} lamports
     * @param {bigint} space
     * @param {Pubkey} owner
     * @returns {Instruction}
     */
    static createAccountWithSeed(from_pubkey, to_pubkey, base, seed, lamports, space, owner) {
        _assertClass(from_pubkey, Pubkey);
        _assertClass(to_pubkey, Pubkey);
        _assertClass(base, Pubkey);
        const ptr0 = passStringToWasm0(seed, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(owner, Pubkey);
        const ret = wasm.systeminstruction_createAccountWithSeed(from_pubkey.__wbg_ptr, to_pubkey.__wbg_ptr, base.__wbg_ptr, ptr0, len0, lamports, space, owner.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} pubkey
     * @param {Pubkey} owner
     * @returns {Instruction}
     */
    static assign(pubkey, owner) {
        _assertClass(pubkey, Pubkey);
        _assertClass(owner, Pubkey);
        const ret = wasm.systeminstruction_assign(pubkey.__wbg_ptr, owner.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} pubkey
     * @param {Pubkey} base
     * @param {string} seed
     * @param {Pubkey} owner
     * @returns {Instruction}
     */
    static assignWithSeed(pubkey, base, seed, owner) {
        _assertClass(pubkey, Pubkey);
        _assertClass(base, Pubkey);
        const ptr0 = passStringToWasm0(seed, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(owner, Pubkey);
        const ret = wasm.systeminstruction_assignWithSeed(pubkey.__wbg_ptr, base.__wbg_ptr, ptr0, len0, owner.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} from_pubkey
     * @param {Pubkey} to_pubkey
     * @param {bigint} lamports
     * @returns {Instruction}
     */
    static transfer(from_pubkey, to_pubkey, lamports) {
        _assertClass(from_pubkey, Pubkey);
        _assertClass(to_pubkey, Pubkey);
        const ret = wasm.systeminstruction_transfer(from_pubkey.__wbg_ptr, to_pubkey.__wbg_ptr, lamports);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} from_pubkey
     * @param {Pubkey} from_base
     * @param {string} from_seed
     * @param {Pubkey} from_owner
     * @param {Pubkey} to_pubkey
     * @param {bigint} lamports
     * @returns {Instruction}
     */
    static transferWithSeed(from_pubkey, from_base, from_seed, from_owner, to_pubkey, lamports) {
        _assertClass(from_pubkey, Pubkey);
        _assertClass(from_base, Pubkey);
        const ptr0 = passStringToWasm0(from_seed, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(from_owner, Pubkey);
        _assertClass(to_pubkey, Pubkey);
        const ret = wasm.systeminstruction_transferWithSeed(from_pubkey.__wbg_ptr, from_base.__wbg_ptr, ptr0, len0, from_owner.__wbg_ptr, to_pubkey.__wbg_ptr, lamports);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} pubkey
     * @param {bigint} space
     * @returns {Instruction}
     */
    static allocate(pubkey, space) {
        _assertClass(pubkey, Pubkey);
        const ret = wasm.systeminstruction_allocate(pubkey.__wbg_ptr, space);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} address
     * @param {Pubkey} base
     * @param {string} seed
     * @param {bigint} space
     * @param {Pubkey} owner
     * @returns {Instruction}
     */
    static allocateWithSeed(address, base, seed, space, owner) {
        _assertClass(address, Pubkey);
        _assertClass(base, Pubkey);
        const ptr0 = passStringToWasm0(seed, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
        const len0 = WASM_VECTOR_LEN;
        _assertClass(owner, Pubkey);
        const ret = wasm.systeminstruction_allocateWithSeed(address.__wbg_ptr, base.__wbg_ptr, ptr0, len0, space, owner.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} from_pubkey
     * @param {Pubkey} nonce_pubkey
     * @param {Pubkey} authority
     * @param {bigint} lamports
     * @returns {Array<any>}
     */
    static createNonceAccount(from_pubkey, nonce_pubkey, authority, lamports) {
        _assertClass(from_pubkey, Pubkey);
        _assertClass(nonce_pubkey, Pubkey);
        _assertClass(authority, Pubkey);
        const ret = wasm.systeminstruction_createNonceAccount(from_pubkey.__wbg_ptr, nonce_pubkey.__wbg_ptr, authority.__wbg_ptr, lamports);
        return ret;
    }
    /**
     * @param {Pubkey} nonce_pubkey
     * @param {Pubkey} authorized_pubkey
     * @returns {Instruction}
     */
    static advanceNonceAccount(nonce_pubkey, authorized_pubkey) {
        _assertClass(nonce_pubkey, Pubkey);
        _assertClass(authorized_pubkey, Pubkey);
        const ret = wasm.systeminstruction_advanceNonceAccount(nonce_pubkey.__wbg_ptr, authorized_pubkey.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} nonce_pubkey
     * @param {Pubkey} authorized_pubkey
     * @param {Pubkey} to_pubkey
     * @param {bigint} lamports
     * @returns {Instruction}
     */
    static withdrawNonceAccount(nonce_pubkey, authorized_pubkey, to_pubkey, lamports) {
        _assertClass(nonce_pubkey, Pubkey);
        _assertClass(authorized_pubkey, Pubkey);
        _assertClass(to_pubkey, Pubkey);
        const ret = wasm.systeminstruction_withdrawNonceAccount(nonce_pubkey.__wbg_ptr, authorized_pubkey.__wbg_ptr, to_pubkey.__wbg_ptr, lamports);
        return Instruction.__wrap(ret);
    }
    /**
     * @param {Pubkey} nonce_pubkey
     * @param {Pubkey} authorized_pubkey
     * @param {Pubkey} new_authority
     * @returns {Instruction}
     */
    static authorizeNonceAccount(nonce_pubkey, authorized_pubkey, new_authority) {
        _assertClass(nonce_pubkey, Pubkey);
        _assertClass(authorized_pubkey, Pubkey);
        _assertClass(new_authority, Pubkey);
        const ret = wasm.systeminstruction_authorizeNonceAccount(nonce_pubkey.__wbg_ptr, authorized_pubkey.__wbg_ptr, new_authority.__wbg_ptr);
        return Instruction.__wrap(ret);
    }
}
module.exports.SystemInstruction = SystemInstruction;

const TransactionFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_transaction_free(ptr >>> 0, 1));
/**
 * wasm-bindgen version of the Transaction struct.
 * This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
 * is fixed. This must not diverge from the regular non-wasm Transaction struct.
 */
class Transaction {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(Transaction.prototype);
        obj.__wbg_ptr = ptr;
        TransactionFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        TransactionFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_transaction_free(ptr, 0);
    }
    /**
     * Create a new `Transaction`
     * @param {Instructions} instructions
     * @param {Pubkey | null} [payer]
     */
    constructor(instructions, payer) {
        _assertClass(instructions, Instructions);
        var ptr0 = instructions.__destroy_into_raw();
        let ptr1 = 0;
        if (!isLikeNone(payer)) {
            _assertClass(payer, Pubkey);
            ptr1 = payer.__destroy_into_raw();
        }
        const ret = wasm.transaction_constructor(ptr0, ptr1);
        this.__wbg_ptr = ret >>> 0;
        TransactionFinalization.register(this, this.__wbg_ptr, this);
        return this;
    }
    /**
     * Return a message containing all data that should be signed.
     * @returns {Message}
     */
    message() {
        const ret = wasm.transaction_message(this.__wbg_ptr);
        return Message.__wrap(ret);
    }
    /**
     * Return the serialized message data to sign.
     * @returns {Uint8Array}
     */
    messageData() {
        const ret = wasm.transaction_messageData(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * Verify the transaction
     */
    verify() {
        const ret = wasm.transaction_verify(this.__wbg_ptr);
        if (ret[1]) {
            throw takeFromExternrefTable0(ret[0]);
        }
    }
    /**
     * @param {Keypair} keypair
     * @param {Hash} recent_blockhash
     */
    partialSign(keypair, recent_blockhash) {
        _assertClass(keypair, Keypair);
        _assertClass(recent_blockhash, Hash);
        wasm.transaction_partialSign(this.__wbg_ptr, keypair.__wbg_ptr, recent_blockhash.__wbg_ptr);
    }
    /**
     * @returns {boolean}
     */
    isSigned() {
        const ret = wasm.transaction_isSigned(this.__wbg_ptr);
        return ret !== 0;
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.transaction_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {Transaction}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.transaction_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return Transaction.__wrap(ret[0]);
    }
}
module.exports.Transaction = Transaction;

const ZeroCiphertextProofFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_zerociphertextproof_free(ptr >>> 0, 1));
/**
 * Zero-ciphertext proof.
 *
 * Contains all the elliptic curve and scalar components that make up the sigma protocol.
 */
class ZeroCiphertextProof {

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        ZeroCiphertextProofFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_zerociphertextproof_free(ptr, 0);
    }
}
module.exports.ZeroCiphertextProof = ZeroCiphertextProof;

const ZeroCiphertextProofContextFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_zerociphertextproofcontext_free(ptr >>> 0, 1));
/**
 * The context data needed to verify a zero-ciphertext proof.
 */
class ZeroCiphertextProofContext {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(ZeroCiphertextProofContext.prototype);
        obj.__wbg_ptr = ptr;
        ZeroCiphertextProofContextFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        ZeroCiphertextProofContextFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_zerociphertextproofcontext_free(ptr, 0);
    }
    /**
     * The ElGamal pubkey associated with the ElGamal ciphertext
     * @returns {PodElGamalPubkey}
     */
    get pubkey() {
        const ret = wasm.__wbg_get_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr);
        return PodElGamalPubkey.__wrap(ret);
    }
    /**
     * The ElGamal pubkey associated with the ElGamal ciphertext
     * @param {PodElGamalPubkey} arg0
     */
    set pubkey(arg0) {
        _assertClass(arg0, PodElGamalPubkey);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_batchedgroupedciphertext2handlesvalidityproofcontext_first_pubkey(this.__wbg_ptr, ptr0);
    }
    /**
     * The ElGamal ciphertext that encrypts zero
     * @returns {PodElGamalCiphertext}
     */
    get ciphertext() {
        const ret = wasm.__wbg_get_ciphertextcommitmentequalityproofcontext_ciphertext(this.__wbg_ptr);
        return PodElGamalCiphertext.__wrap(ret);
    }
    /**
     * The ElGamal ciphertext that encrypts zero
     * @param {PodElGamalCiphertext} arg0
     */
    set ciphertext(arg0) {
        _assertClass(arg0, PodElGamalCiphertext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_ciphertextcommitmentequalityproofcontext_ciphertext(this.__wbg_ptr, ptr0);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.zerociphertextproofcontext_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {ZeroCiphertextProofContext}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.zerociphertextproofcontext_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return ZeroCiphertextProofContext.__wrap(ret[0]);
    }
}
module.exports.ZeroCiphertextProofContext = ZeroCiphertextProofContext;

const ZeroCiphertextProofDataFinalization = (typeof FinalizationRegistry === 'undefined')
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry(ptr => wasm.__wbg_zerociphertextproofdata_free(ptr >>> 0, 1));
/**
 * The instruction data that is needed for the `ProofInstruction::ZeroCiphertext` instruction.
 *
 * It includes the cryptographic proof as well as the context data information needed to verify
 * the proof.
 */
class ZeroCiphertextProofData {

    static __wrap(ptr) {
        ptr = ptr >>> 0;
        const obj = Object.create(ZeroCiphertextProofData.prototype);
        obj.__wbg_ptr = ptr;
        ZeroCiphertextProofDataFinalization.register(obj, obj.__wbg_ptr, obj);
        return obj;
    }

    __destroy_into_raw() {
        const ptr = this.__wbg_ptr;
        this.__wbg_ptr = 0;
        ZeroCiphertextProofDataFinalization.unregister(this);
        return ptr;
    }

    free() {
        const ptr = this.__destroy_into_raw();
        wasm.__wbg_zerociphertextproofdata_free(ptr, 0);
    }
    /**
     * The context data for the zero-ciphertext proof
     * @returns {ZeroCiphertextProofContext}
     */
    get context() {
        const ret = wasm.__wbg_get_zerociphertextproofdata_context(this.__wbg_ptr);
        return ZeroCiphertextProofContext.__wrap(ret);
    }
    /**
     * The context data for the zero-ciphertext proof
     * @param {ZeroCiphertextProofContext} arg0
     */
    set context(arg0) {
        _assertClass(arg0, ZeroCiphertextProofContext);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_zerociphertextproofdata_context(this.__wbg_ptr, ptr0);
    }
    /**
     * Proof that the ciphertext is zero
     * @returns {PodZeroCiphertextProof}
     */
    get proof() {
        const ret = wasm.__wbg_get_zerociphertextproofdata_proof(this.__wbg_ptr);
        return PodZeroCiphertextProof.__wrap(ret);
    }
    /**
     * Proof that the ciphertext is zero
     * @param {PodZeroCiphertextProof} arg0
     */
    set proof(arg0) {
        _assertClass(arg0, PodZeroCiphertextProof);
        var ptr0 = arg0.__destroy_into_raw();
        wasm.__wbg_set_zerociphertextproofdata_proof(this.__wbg_ptr, ptr0);
    }
    /**
     * @param {ElGamalKeypair} keypair
     * @param {ElGamalCiphertext} ciphertext
     * @returns {ZeroCiphertextProofData}
     */
    static new(keypair, ciphertext) {
        _assertClass(keypair, ElGamalKeypair);
        _assertClass(ciphertext, ElGamalCiphertext);
        const ret = wasm.zerociphertextproofdata_new(keypair.__wbg_ptr, ciphertext.__wbg_ptr);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return ZeroCiphertextProofData.__wrap(ret[0]);
    }
    /**
     * @returns {Uint8Array}
     */
    toBytes() {
        const ret = wasm.zerociphertextproofdata_toBytes(this.__wbg_ptr);
        var v1 = getArrayU8FromWasm0(ret[0], ret[1]).slice();
        wasm.__wbindgen_free(ret[0], ret[1] * 1, 1);
        return v1;
    }
    /**
     * @param {Uint8Array} bytes
     * @returns {ZeroCiphertextProofData}
     */
    static fromBytes(bytes) {
        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_malloc);
        const len0 = WASM_VECTOR_LEN;
        const ret = wasm.zerociphertextproofdata_fromBytes(ptr0, len0);
        if (ret[2]) {
            throw takeFromExternrefTable0(ret[1]);
        }
        return ZeroCiphertextProofData.__wrap(ret[0]);
    }
}
module.exports.ZeroCiphertextProofData = ZeroCiphertextProofData;

module.exports.__wbg_buffer_609cc3eee51ed158 = function(arg0) {
    const ret = arg0.buffer;
    return ret;
};

module.exports.__wbg_call_672a4d21634d4a24 = function() { return handleError(function (arg0, arg1) {
    const ret = arg0.call(arg1);
    return ret;
}, arguments) };

module.exports.__wbg_call_7cccdd69e0791ae2 = function() { return handleError(function (arg0, arg1, arg2) {
    const ret = arg0.call(arg1, arg2);
    return ret;
}, arguments) };

module.exports.__wbg_crypto_038798f665f985e2 = function(arg0) {
    const ret = arg0.crypto;
    return ret;
};

module.exports.__wbg_crypto_ed58b8e10a292839 = function(arg0) {
    const ret = arg0.crypto;
    return ret;
};

module.exports.__wbg_done_769e5ede4b31c67b = function(arg0) {
    const ret = arg0.done;
    return ret;
};

module.exports.__wbg_error_7534b8e9a36f1ab4 = function(arg0, arg1) {
    let deferred0_0;
    let deferred0_1;
    try {
        deferred0_0 = arg0;
        deferred0_1 = arg1;
        console.error(getStringFromWasm0(arg0, arg1));
    } finally {
        wasm.__wbindgen_free(deferred0_0, deferred0_1, 1);
    }
};

module.exports.__wbg_getRandomValues_371e7ade8bd92088 = function(arg0, arg1) {
    arg0.getRandomValues(arg1);
};

module.exports.__wbg_getRandomValues_7dfe5bd1b67c9ca1 = function(arg0) {
    const ret = arg0.getRandomValues;
    return ret;
};

module.exports.__wbg_getRandomValues_bcb4912f16000dc4 = function() { return handleError(function (arg0, arg1) {
    arg0.getRandomValues(arg1);
}, arguments) };

module.exports.__wbg_get_67b2ba62fc30de12 = function() { return handleError(function (arg0, arg1) {
    const ret = Reflect.get(arg0, arg1);
    return ret;
}, arguments) };

module.exports.__wbg_instanceof_Uint8Array_17156bcf118086a9 = function(arg0) {
    let result;
    try {
        result = arg0 instanceof Uint8Array;
    } catch (_) {
        result = false;
    }
    const ret = result;
    return ret;
};

module.exports.__wbg_instruction_new = function(arg0) {
    const ret = Instruction.__wrap(arg0);
    return ret;
};

module.exports.__wbg_isArray_a1eab7e0d067391b = function(arg0) {
    const ret = Array.isArray(arg0);
    return ret;
};

module.exports.__wbg_iterator_9a24c88df860dc65 = function() {
    const ret = Symbol.iterator;
    return ret;
};

module.exports.__wbg_length_a446193dc22c12f8 = function(arg0) {
    const ret = arg0.length;
    return ret;
};

module.exports.__wbg_msCrypto_0a36e2ec3a343d26 = function(arg0) {
    const ret = arg0.msCrypto;
    return ret;
};

module.exports.__wbg_msCrypto_ff35fce085fab2a3 = function(arg0) {
    const ret = arg0.msCrypto;
    return ret;
};

module.exports.__wbg_new_78feb108b6472713 = function() {
    const ret = new Array();
    return ret;
};

module.exports.__wbg_new_8a6f238a6ece86ea = function() {
    const ret = new Error();
    return ret;
};

module.exports.__wbg_new_a12002a7f91c75be = function(arg0) {
    const ret = new Uint8Array(arg0);
    return ret;
};

module.exports.__wbg_new_c68d7209be747379 = function(arg0, arg1) {
    const ret = new Error(getStringFromWasm0(arg0, arg1));
    return ret;
};

module.exports.__wbg_newnoargs_105ed471475aaf50 = function(arg0, arg1) {
    const ret = new Function(getStringFromWasm0(arg0, arg1));
    return ret;
};

module.exports.__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a = function(arg0, arg1, arg2) {
    const ret = new Uint8Array(arg0, arg1 >>> 0, arg2 >>> 0);
    return ret;
};

module.exports.__wbg_newwithlength_a381634e90c276d4 = function(arg0) {
    const ret = new Uint8Array(arg0 >>> 0);
    return ret;
};

module.exports.__wbg_newwithlength_c4c419ef0bc8a1f8 = function(arg0) {
    const ret = new Array(arg0 >>> 0);
    return ret;
};

module.exports.__wbg_next_25feadfc0913fea9 = function(arg0) {
    const ret = arg0.next;
    return ret;
};

module.exports.__wbg_next_6574e1a8a62d1055 = function() { return handleError(function (arg0) {
    const ret = arg0.next();
    return ret;
}, arguments) };

module.exports.__wbg_node_02999533c4ea02e3 = function(arg0) {
    const ret = arg0.node;
    return ret;
};

module.exports.__wbg_process_5c1d670bc53614b8 = function(arg0) {
    const ret = arg0.process;
    return ret;
};

module.exports.__wbg_pubkey_new = function(arg0) {
    const ret = Pubkey.__wrap(arg0);
    return ret;
};

module.exports.__wbg_push_737cfc8c1432c2c6 = function(arg0, arg1) {
    const ret = arg0.push(arg1);
    return ret;
};

module.exports.__wbg_randomFillSync_994ac6d9ade7a695 = function(arg0, arg1, arg2) {
    arg0.randomFillSync(getArrayU8FromWasm0(arg1, arg2));
};

module.exports.__wbg_randomFillSync_ab2cfe79ebbf2740 = function() { return handleError(function (arg0, arg1) {
    arg0.randomFillSync(arg1);
}, arguments) };

module.exports.__wbg_require_0d6aeaec3c042c88 = function(arg0, arg1, arg2) {
    const ret = arg0.require(getStringFromWasm0(arg1, arg2));
    return ret;
};

module.exports.__wbg_require_79b1e9274cde3c87 = function() { return handleError(function () {
    const ret = module.require;
    return ret;
}, arguments) };

module.exports.__wbg_self_25aabeb5a7b41685 = function() { return handleError(function () {
    const ret = self.self;
    return ret;
}, arguments) };

module.exports.__wbg_set_37837023f3d740e8 = function(arg0, arg1, arg2) {
    arg0[arg1 >>> 0] = arg2;
};

module.exports.__wbg_set_65595bdd868b3009 = function(arg0, arg1, arg2) {
    arg0.set(arg1, arg2 >>> 0);
};

module.exports.__wbg_stack_0ed75d68575b0f3c = function(arg0, arg1) {
    const ret = arg1.stack;
    const ptr1 = passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
    const len1 = WASM_VECTOR_LEN;
    getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);
    getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);
};

module.exports.__wbg_static_accessor_GLOBAL_88a902d13a557d07 = function() {
    const ret = typeof global === 'undefined' ? null : global;
    return isLikeNone(ret) ? 0 : addToExternrefTable0(ret);
};

module.exports.__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0 = function() {
    const ret = typeof globalThis === 'undefined' ? null : globalThis;
    return isLikeNone(ret) ? 0 : addToExternrefTable0(ret);
};

module.exports.__wbg_static_accessor_MODULE_ef3aa2eb251158a5 = function() {
    const ret = module;
    return ret;
};

module.exports.__wbg_static_accessor_SELF_37c5d418e4bf5819 = function() {
    const ret = typeof self === 'undefined' ? null : self;
    return isLikeNone(ret) ? 0 : addToExternrefTable0(ret);
};

module.exports.__wbg_static_accessor_WINDOW_5de37043a91a9c40 = function() {
    const ret = typeof window === 'undefined' ? null : window;
    return isLikeNone(ret) ? 0 : addToExternrefTable0(ret);
};

module.exports.__wbg_subarray_aa9065fa9dc5df96 = function(arg0, arg1, arg2) {
    const ret = arg0.subarray(arg1 >>> 0, arg2 >>> 0);
    return ret;
};

module.exports.__wbg_value_cd1ffa7b1ab794f1 = function(arg0) {
    const ret = arg0.value;
    return ret;
};

module.exports.__wbg_values_99f7a68c7f313d66 = function(arg0) {
    const ret = arg0.values();
    return ret;
};

module.exports.__wbg_versions_c71aa1626a93e0a1 = function(arg0) {
    const ret = arg0.versions;
    return ret;
};

module.exports.__wbindgen_debug_string = function(arg0, arg1) {
    const ret = debugString(arg1);
    const ptr1 = passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
    const len1 = WASM_VECTOR_LEN;
    getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);
    getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);
};

module.exports.__wbindgen_error_new = function(arg0, arg1) {
    const ret = new Error(getStringFromWasm0(arg0, arg1));
    return ret;
};

module.exports.__wbindgen_init_externref_table = function() {
    const table = wasm.__wbindgen_export_2;
    const offset = table.grow(4);
    table.set(0, undefined);
    table.set(offset + 0, undefined);
    table.set(offset + 1, null);
    table.set(offset + 2, true);
    table.set(offset + 3, false);
    ;
};

module.exports.__wbindgen_is_function = function(arg0) {
    const ret = typeof(arg0) === 'function';
    return ret;
};

module.exports.__wbindgen_is_object = function(arg0) {
    const val = arg0;
    const ret = typeof(val) === 'object' && val !== null;
    return ret;
};

module.exports.__wbindgen_is_string = function(arg0) {
    const ret = typeof(arg0) === 'string';
    return ret;
};

module.exports.__wbindgen_is_undefined = function(arg0) {
    const ret = arg0 === undefined;
    return ret;
};

module.exports.__wbindgen_memory = function() {
    const ret = wasm.memory;
    return ret;
};

module.exports.__wbindgen_number_get = function(arg0, arg1) {
    const obj = arg1;
    const ret = typeof(obj) === 'number' ? obj : undefined;
    getDataViewMemory0().setFloat64(arg0 + 8 * 1, isLikeNone(ret) ? 0 : ret, true);
    getDataViewMemory0().setInt32(arg0 + 4 * 0, !isLikeNone(ret), true);
};

module.exports.__wbindgen_number_new = function(arg0) {
    const ret = arg0;
    return ret;
};

module.exports.__wbindgen_string_get = function(arg0, arg1) {
    const obj = arg1;
    const ret = typeof(obj) === 'string' ? obj : undefined;
    var ptr1 = isLikeNone(ret) ? 0 : passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);
    var len1 = WASM_VECTOR_LEN;
    getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);
    getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);
};

module.exports.__wbindgen_string_new = function(arg0, arg1) {
    const ret = getStringFromWasm0(arg0, arg1);
    return ret;
};

module.exports.__wbindgen_throw = function(arg0, arg1) {
    throw new Error(getStringFromWasm0(arg0, arg1));
};

const path = require('path').join(__dirname, 'yellowstone_grpc_solana_encoding_wasm_bg.wasm');
const bytes = require('fs').readFileSync(path);

const wasmModule = new WebAssembly.Module(bytes);
const wasmInstance = new WebAssembly.Instance(wasmModule, imports);
wasm = wasmInstance.exports;
module.exports.__wasm = wasm;

wasm.__wbindgen_start();

