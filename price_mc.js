require('dotenv').config();
const { Connection, PublicKey, clusterApiUrl } = require('@solana/web3.js');
const Client = require('@triton-one/yellowstone-grpc').default;

class RealTimePriceMarketCap {
    constructor() {
        // Load token addresses from environment
        this.tokenMintAddress = process.env.TOKEN_MINT_ADDRESS;
        this.tokenAddress = process.env.TOKEN_ADDRESS;
        this.walletAddress = process.env.WALLET_ADDRESS;
        this.poolAddress = process.env.POOL_ADDRESS;

        // gRPC and RPC setup
        this.grpcUrl = process.env.SOLANA_GRPC_URL || 'solana-yellowstone-grpc.publicnode.com:443';
        this.rpcUrl = process.env.SOLANA_RPC_URL || clusterApiUrl('mainnet-beta');

        // Initialize connections
        this.grpcClient = new Client(`https://${this.grpcUrl}`, undefined, {
            'grpc.keepalive_time_ms': 30000,
            'grpc.keepalive_timeout_ms': 5000,
            'grpc.keepalive_permit_without_calls': true,
            'grpc.http2.max_pings_without_data': 0,
            'grpc.http2.min_time_between_pings_ms': 10000,
            'grpc.http2.min_ping_interval_without_data_ms': 300000
        });

        this.rpcConnection = new Connection(this.rpcUrl, 'confirmed');

        // Cache and state management
        this.priceCache = new Map();
        this.marketCapCache = new Map();
        this.tokenSupplyCache = new Map();
        this.poolDataCache = new Map();
        this.isMonitoring = false;

        // Known DEX program IDs for price calculation
        this.dexPrograms = {
            PUMP_FUN: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
            PUMP_SWAP: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
            RAYDIUM_V4: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
            RAYDIUM_V5: '5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h',
            ORCA: '9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP',
            SERUM_V3: '9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin',
        };

        this.validateInputs();

        console.log(`🔗 gRPC Connected to: ${this.grpcUrl}`);
        console.log(`🔗 RPC Connected to: ${this.rpcUrl}`);
        console.log(`🪙 Token Mint: ${this.tokenMintAddress}`);
        console.log(`📍 Token Address: ${this.tokenAddress}`);
        console.log(`🏊 Pool Address: ${this.poolAddress}`);
    }

    validateInputs() {
        if (!this.tokenMintAddress) {
            throw new Error('TOKEN_MINT_ADDRESS not found in .env file');
        }
        if (!this.tokenAddress) {
            throw new Error('TOKEN_ADDRESS not found in .env file');
        }
        if (!this.poolAddress) {
            throw new Error('POOL_ADDRESS not found in .env file');
        }

        try {
            new PublicKey(this.tokenMintAddress);
            new PublicKey(this.tokenAddress);
            new PublicKey(this.poolAddress);
        } catch (error) {
            throw new Error(`Invalid address format: ${error.message}`);
        }
    }

    /**
     * Get token supply from on-chain data
     */
    async getTokenSupply(mintAddress) {
        if (this.tokenSupplyCache.has(mintAddress)) {
            return this.tokenSupplyCache.get(mintAddress);
        }

        try {
            const mintPublicKey = new PublicKey(mintAddress);
            const supply = await this.rpcConnection.getTokenSupply(mintPublicKey);

            const supplyData = {
                total: supply.value.amount,
                decimals: supply.value.decimals,
                totalSupply: parseFloat(supply.value.amount) / Math.pow(10, supply.value.decimals)
            };

            this.tokenSupplyCache.set(mintAddress, supplyData);
            return supplyData;

        } catch (error) {
            console.error(`❌ Error fetching token supply for ${mintAddress}:`, error.message);
            return null;
        }
    }

    /**
     * Get pool data directly from pool address
     */
    async getPoolData(poolAddress) {
        try {
            console.log(`🔍 Getting pool data from address: ${poolAddress}`);

            const poolPublicKey = new PublicKey(poolAddress);
            const accountInfo = await this.rpcConnection.getAccountInfo(poolPublicKey);

            if (!accountInfo || !accountInfo.data) {
                console.log(`❌ No account data found for pool address: ${poolAddress}`);
                return null;
            }

            console.log(`📊 Pool account data length: ${accountInfo.data.length} bytes`);
            console.log(`📊 Pool account owner: ${accountInfo.owner.toBase58()}`);

            // Determine pool type based on owner program
            let poolType = 'unknown';
            const ownerProgram = accountInfo.owner.toBase58();

            if (ownerProgram === this.dexPrograms.PUMP_FUN) {
                poolType = 'pumpfun';
            } else if (ownerProgram === this.dexPrograms.PUMP_SWAP) {
                poolType = 'pumpswap';
            } else if (ownerProgram === this.dexPrograms.RAYDIUM_V4) {
                poolType = 'raydium';
            } else if (ownerProgram === this.dexPrograms.ORCA) {
                poolType = 'orca';
            }

            console.log(`🏊 Detected pool type: ${poolType}`);

            // Parse pool data based on type
            const mockAccount = {
                pubkey: poolPublicKey,
                account: accountInfo
            };

            let poolData = null;
            switch (poolType) {
                case 'pumpfun':
                    poolData = await this.parsePumpFunPoolData(mockAccount);
                    break;
                case 'pumpswap':
                    poolData = await this.parsePumpSwapPoolData(mockAccount);
                    break;
                case 'raydium':
                    poolData = await this.parseRaydiumPoolData(mockAccount);
                    break;
                case 'orca':
                    poolData = await this.parseOrcaPoolData(mockAccount);
                    break;
                default:
                    console.log(`⚠️  Unknown pool type for owner: ${ownerProgram}`);
                    return null;
            }

            if (poolData) {
                console.log(`✅ Successfully parsed ${poolType} pool data`);
                return poolData;
            } else {
                console.log(`❌ Failed to parse pool data`);
                return null;
            }

        } catch (error) {
            console.error(`❌ Error getting pool data:`, error.message);
            return null;
        }
    }

    /**
     * Find liquidity pools for the token (fallback method)
     */
    async findLiquidityPools(tokenMint) {
        try {
            // If we have a specific pool address, use it directly
            if (this.poolAddress) {
                console.log(`🎯 Using provided pool address: ${this.poolAddress}`);
                const poolData = await this.getPoolData(this.poolAddress);
                return poolData ? [poolData] : [];
            }

            // Otherwise, search for pools (original logic)
            const tokenMintKey = new PublicKey(tokenMint);
            const pools = [];

            console.log(`🔍 Searching for liquidity pools for token: ${tokenMint}`);

            // Search for Pump.fun pools (most common for new tokens)
            const pumpFunPools = await this.findPumpFunPools(tokenMintKey);
            pools.push(...pumpFunPools);

            // Search for PumpSwap pools
            const pumpSwapPools = await this.findPumpSwapPools(tokenMintKey);
            pools.push(...pumpSwapPools);

            // Search for Raydium pools
            const raydiumPools = await this.findRaydiumPools(tokenMintKey);
            pools.push(...raydiumPools);

            // Search for Orca pools
            const orcaPools = await this.findOrcaPools(tokenMintKey);
            pools.push(...orcaPools);

            console.log(`🏊 Found ${pools.length} total liquidity pools for token ${tokenMint}`);
            return pools;

        } catch (error) {
            console.error(`❌ Error finding liquidity pools:`, error.message);
            return [];
        }
    }

    /**
     * Find Pump.fun pools
     */
    async findPumpFunPools(tokenMint) {
        const pools = [];

        try {
            console.log(`🔍 Searching for Pump.fun pools...`);

            // Get all accounts owned by Pump.fun program
            const pumpFunAccounts = await this.rpcConnection.getProgramAccounts(
                new PublicKey(this.dexPrograms.PUMP_FUN),
                {
                    filters: [
                        {
                            memcmp: {
                                offset: 8, // Token mint offset in Pump.fun pool
                                bytes: tokenMint.toBase58()
                            }
                        }
                    ]
                }
            );

            console.log(`📊 Found ${pumpFunAccounts.length} Pump.fun accounts`);

            for (const account of pumpFunAccounts) {
                const poolData = await this.parsePumpFunPoolData(account);
                if (poolData) {
                    pools.push(poolData);
                }
            }

        } catch (error) {
            console.error(`❌ Error finding Pump.fun pools:`, error.message);
        }

        console.log(`🏊 Found ${pools.length} Pump.fun pools`);
        return pools;
    }

    /**
     * Find PumpSwap pools
     */
    async findPumpSwapPools(tokenMint) {
        const pools = [];

        try {
            console.log(`🔍 Searching for PumpSwap pools...`);

            // Get all accounts owned by PumpSwap program
            const pumpSwapAccounts = await this.rpcConnection.getProgramAccounts(
                new PublicKey(this.dexPrograms.PUMP_SWAP),
                {
                    filters: [
                        {
                            memcmp: {
                                offset: 8, // Token mint offset in PumpSwap pool
                                bytes: tokenMint.toBase58()
                            }
                        }
                    ]
                }
            );

            console.log(`📊 Found ${pumpSwapAccounts.length} PumpSwap accounts`);

            for (const account of pumpSwapAccounts) {
                const poolData = await this.parsePumpSwapPoolData(account);
                if (poolData) {
                    pools.push(poolData);
                }
            }

        } catch (error) {
            console.error(`❌ Error finding PumpSwap pools:`, error.message);
        }

        console.log(`🏊 Found ${pools.length} PumpSwap pools`);
        return pools;
    }

    /**
     * Find Raydium liquidity pools
     */
    async findRaydiumPools(tokenMint) {
        const pools = [];

        try {
            console.log(`🔍 Searching for Raydium pools for token: ${tokenMint.toBase58()}`);

            // Search for pools where our token is either tokenA or tokenB
            const searchFilters = [
                // Token as tokenA
                {
                    memcmp: {
                        offset: 400, // Token A mint offset in Raydium pool
                        bytes: tokenMint.toBase58()
                    }
                },
                // Token as tokenB
                {
                    memcmp: {
                        offset: 432, // Token B mint offset in Raydium pool
                        bytes: tokenMint.toBase58()
                    }
                }
            ];

            for (const filter of searchFilters) {
                try {
                    const raydiumAccounts = await this.rpcConnection.getProgramAccounts(
                        new PublicKey(this.dexPrograms.RAYDIUM_V4),
                        {
                            filters: [filter]
                        }
                    );

                    console.log(`📊 Found ${raydiumAccounts.length} Raydium V4 accounts with filter`);

                    for (const account of raydiumAccounts) {
                        const poolData = await this.parseRaydiumPoolData(account);
                        if (poolData) {
                            pools.push(poolData);
                        }
                    }
                } catch (filterError) {
                    console.error(`❌ Error with Raydium filter:`, filterError.message);
                }
            }

            // Also try Raydium V5 if V4 doesn't work
            if (pools.length === 0) {
                console.log(`🔍 Trying Raydium V5 program...`);
                try {
                    const raydiumV5Accounts = await this.rpcConnection.getProgramAccounts(
                        new PublicKey(this.dexPrograms.RAYDIUM_V5)
                    );
                    console.log(`📊 Found ${raydiumV5Accounts.length} Raydium V5 accounts`);
                } catch (v5Error) {
                    console.error(`❌ Error searching Raydium V5:`, v5Error.message);
                }
            }

        } catch (error) {
            console.error(`❌ Error finding Raydium pools:`, error.message);
        }

        console.log(`🏊 Found ${pools.length} Raydium pools`);
        return pools;
    }

    /**
     * Find Orca liquidity pools
     */
    async findOrcaPools(tokenMint) {
        const pools = [];

        try {
            // Get all accounts owned by Orca program
            const orcaAccounts = await this.rpcConnection.getProgramAccounts(
                new PublicKey(this.dexPrograms.ORCA),
                {
                    filters: [
                        {
                            memcmp: {
                                offset: 101, // Token A mint offset in Orca pool
                                bytes: tokenMint.toBase58()
                            }
                        }
                    ]
                }
            );

            for (const account of orcaAccounts) {
                const poolData = await this.parseOrcaPoolData(account);
                if (poolData) {
                    pools.push(poolData);
                }
            }

        } catch (error) {
            console.error(`❌ Error finding Orca pools:`, error.message);
        }

        return pools;
    }

    /**
     * Parse Raydium pool data to extract price information
     */
    async parseRaydiumPoolData(account) {
        try {
            const data = account.account.data;
            if (data.length < 752) return null; // Minimum size for Raydium pool

            // Parse pool data (simplified - actual parsing would be more complex)
            const poolInfo = {
                address: account.pubkey.toBase58(),
                type: 'raydium',
                tokenAMint: new PublicKey(data.slice(400, 432)).toBase58(),
                tokenBMint: new PublicKey(data.slice(432, 464)).toBase58(),
                tokenAVault: new PublicKey(data.slice(464, 496)).toBase58(),
                tokenBVault: new PublicKey(data.slice(496, 528)).toBase58(),
            };

            return poolInfo;

        } catch (error) {
            console.error(`❌ Error parsing Raydium pool data:`, error.message);
            return null;
        }
    }

    /**
     * Parse Orca pool data to extract price information
     */
    async parseOrcaPoolData(account) {
        try {
            const data = account.account.data;
            if (data.length < 324) return null; // Minimum size for Orca pool

            // Parse pool data (simplified - actual parsing would be more complex)
            const poolInfo = {
                address: account.pubkey.toBase58(),
                type: 'orca',
                tokenAMint: new PublicKey(data.slice(101, 133)).toBase58(),
                tokenBMint: new PublicKey(data.slice(133, 165)).toBase58(),
                tokenAVault: new PublicKey(data.slice(165, 197)).toBase58(),
                tokenBVault: new PublicKey(data.slice(197, 229)).toBase58(),
            };

            return poolInfo;

        } catch (error) {
            console.error(`❌ Error parsing Orca pool data:`, error.message);
            return null;
        }
    }

    /**
     * Parse Pump.fun pool data to extract price information
     */
    async parsePumpFunPoolData(account) {
        try {
            const data = account.account.data;
            console.log(`🔍 Parsing Pump.fun pool data (${data.length} bytes)`);

            if (data.length < 100) {
                console.log(`❌ Pool data too small: ${data.length} bytes`);
                return null;
            }

            // Log first few bytes for debugging
            console.log(`📊 First 32 bytes:`, Array.from(data.slice(0, 32)).map(b => b.toString(16).padStart(2, '0')).join(' '));

            // Try different parsing approaches for Pump.fun bonding curve
            let poolInfo = null;

            // Approach 1: Standard bonding curve layout
            try {
                poolInfo = {
                    address: account.pubkey.toBase58(),
                    type: 'pumpfun',
                    tokenMint: new PublicKey(data.slice(8, 40)).toBase58(),
                    bondingCurve: account.pubkey.toBase58(), // The account itself is the bonding curve
                    // Try reading reserves from different offsets
                    virtualTokenReserves: this.readUint64LE(data, 40),
                    virtualSolReserves: this.readUint64LE(data, 48),
                    realTokenReserves: this.readUint64LE(data, 56),
                    realSolReserves: this.readUint64LE(data, 64),
                    tokenTotalSupply: this.readUint64LE(data, 72),
                    complete: data.length > 80 ? data[80] === 1 : false
                };

                console.log(`📊 Parsed pool info:`, {
                    tokenMint: poolInfo.tokenMint,
                    virtualTokenReserves: poolInfo.virtualTokenReserves,
                    virtualSolReserves: poolInfo.virtualSolReserves,
                    realTokenReserves: poolInfo.realTokenReserves,
                    realSolReserves: poolInfo.realSolReserves
                });

            } catch (parseError) {
                console.log(`⚠️  Standard parsing failed, trying alternative approach`);

                // Approach 2: Alternative layout
                poolInfo = {
                    address: account.pubkey.toBase58(),
                    type: 'pumpfun',
                    tokenMint: this.tokenMintAddress, // Use the token mint from env
                    bondingCurve: account.pubkey.toBase58(),
                    // Use simpler approach - just try to read some reserves
                    virtualTokenReserves: this.readUint64LE(data, 16),
                    virtualSolReserves: this.readUint64LE(data, 24),
                    realTokenReserves: this.readUint64LE(data, 32),
                    realSolReserves: this.readUint64LE(data, 40),
                    tokenTotalSupply: **********, // Default supply for pump.fun tokens
                    complete: false
                };
            }

            // Validate that we have some meaningful data
            if (poolInfo && (poolInfo.virtualSolReserves > 0 || poolInfo.realSolReserves > 0)) {
                console.log(`✅ Successfully parsed Pump.fun pool data`);
                return poolInfo;
            } else {
                console.log(`❌ No valid reserves found in pool data`);
                return null;
            }

        } catch (error) {
            console.error(`❌ Error parsing Pump.fun pool data:`, error.message);
            return null;
        }
    }

    /**
     * Parse PumpSwap pool data to extract price information
     */
    async parsePumpSwapPoolData(account) {
        try {
            const data = account.account.data;
            if (data.length < 180) return null; // Minimum size for PumpSwap pool

            // Parse PumpSwap pool data structure
            const poolInfo = {
                address: account.pubkey.toBase58(),
                type: 'pumpswap',
                tokenMint: new PublicKey(data.slice(8, 40)).toBase58(),
                quoteMint: new PublicKey(data.slice(40, 72)).toBase58(),
                tokenVault: new PublicKey(data.slice(72, 104)).toBase58(),
                quoteVault: new PublicKey(data.slice(104, 136)).toBase58(),
                tokenReserves: this.readUint64LE(data, 136),
                quoteReserves: this.readUint64LE(data, 144),
                lpSupply: this.readUint64LE(data, 152)
            };

            return poolInfo;

        } catch (error) {
            console.error(`❌ Error parsing PumpSwap pool data:`, error.message);
            return null;
        }
    }

    /**
     * Helper method to read uint64 little endian from buffer
     */
    readUint64LE(buffer, offset) {
        try {
            const dataView = new DataView(buffer.buffer, buffer.byteOffset + offset, 8);
            return Number(dataView.getBigUint64(0, true)); // true for little endian
        } catch (error) {
            return 0;
        }
    }

    /**
     * Calculate token price from pool reserves
     */
    async calculateTokenPrice(tokenMint, pools) {
        let totalPrice = 0;
        let validPools = 0;

        for (const pool of pools) {
            try {
                const price = await this.calculatePoolPrice(tokenMint, pool);
                if (price > 0) {
                    totalPrice += price;
                    validPools++;
                }
            } catch (error) {
                console.error(`❌ Error calculating price for pool ${pool.address}:`, error.message);
            }
        }

        if (validPools === 0) {
            console.log(`⚠️  No valid pools found for price calculation`);
            return 0;
        }

        // Return average price across all pools
        const averagePrice = totalPrice / validPools;
        console.log(`💰 Calculated average price: $${averagePrice.toFixed(6)} from ${validPools} pools`);

        return averagePrice;
    }

    /**
     * Calculate price from individual pool
     */
    async calculatePoolPrice(tokenMint, pool) {
        try {
            let price = 0;
            const solMint = 'So11111111111111111111111111111111111111112';
            const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

            // Handle different pool types
            switch (pool.type) {
                case 'pumpfun':
                    price = await this.calculatePumpFunPrice(tokenMint, pool);
                    break;

                case 'pumpswap':
                    price = await this.calculatePumpSwapPrice(tokenMint, pool);
                    break;

                case 'raydium':
                case 'orca':
                    // Traditional AMM pools
                    const tokenABalance = await this.getTokenBalance(pool.tokenAVault);
                    const tokenBBalance = await this.getTokenBalance(pool.tokenBVault);

                    if (!tokenABalance || !tokenBBalance || tokenABalance === 0 || tokenBBalance === 0) {
                        return 0;
                    }

                    if (pool.tokenAMint === tokenMint) {
                        // Our token is tokenA
                        if (pool.tokenBMint === solMint) {
                            // Price in SOL - need to convert to USD (assuming SOL = $100 for now)
                            price = (tokenBBalance / tokenABalance) * 100;
                        } else if (pool.tokenBMint === usdcMint) {
                            // Price in USDC (direct USD price)
                            price = tokenBBalance / tokenABalance;
                        }
                    } else if (pool.tokenBMint === tokenMint) {
                        // Our token is tokenB
                        if (pool.tokenAMint === solMint) {
                            // Price in SOL - need to convert to USD
                            price = (tokenABalance / tokenBBalance) * 100;
                        } else if (pool.tokenAMint === usdcMint) {
                            // Price in USDC (direct USD price)
                            price = tokenABalance / tokenBBalance;
                        }
                    }
                    break;

                default:
                    console.log(`⚠️  Unknown pool type: ${pool.type}`);
                    return 0;
            }

            if (price > 0) {
                console.log(`💰 Pool ${pool.address} (${pool.type}): $${price.toFixed(8)}`);
            }

            return price;

        } catch (error) {
            console.error(`❌ Error calculating pool price:`, error.message);
            return 0;
        }
    }

    /**
     * Calculate price from Pump.fun bonding curve
     */
    async calculatePumpFunPrice(tokenMint, pool) {
        try {
            // Pump.fun uses a bonding curve mechanism
            const virtualSolReserves = pool.virtualSolReserves;
            const virtualTokenReserves = pool.virtualTokenReserves;
            const realSolReserves = pool.realSolReserves;
            const realTokenReserves = pool.realTokenReserves;

            if (virtualTokenReserves === 0 || virtualSolReserves === 0) {
                return 0;
            }

            // Calculate price based on bonding curve formula
            // Price = (virtualSolReserves + realSolReserves) / (virtualTokenReserves + realTokenReserves)
            const totalSolReserves = virtualSolReserves + realSolReserves;
            const totalTokenReserves = virtualTokenReserves + realTokenReserves;

            if (totalTokenReserves === 0) return 0;

            // Convert from lamports to SOL and assume SOL = $100
            const priceInSol = (totalSolReserves / 1e9) / (totalTokenReserves / 1e6); // Assuming 6 decimals for token
            const priceInUsd = priceInSol * 100; // Assuming SOL = $100

            return priceInUsd;

        } catch (error) {
            console.error(`❌ Error calculating Pump.fun price:`, error.message);
            return 0;
        }
    }

    /**
     * Calculate price from PumpSwap pool
     */
    async calculatePumpSwapPrice(tokenMint, pool) {
        try {
            const tokenReserves = pool.tokenReserves;
            const quoteReserves = pool.quoteReserves;

            if (tokenReserves === 0 || quoteReserves === 0) {
                return 0;
            }

            // Simple AMM formula: price = quoteReserves / tokenReserves
            const solMint = 'So11111111111111111111111111111111111111112';
            const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

            let price = 0;
            if (pool.quoteMint === solMint) {
                // Quote is SOL, convert to USD
                const priceInSol = (quoteReserves / 1e9) / (tokenReserves / 1e6); // Assuming 6 decimals
                price = priceInSol * 100; // Assuming SOL = $100
            } else if (pool.quoteMint === usdcMint) {
                // Quote is USDC, direct USD price
                price = (quoteReserves / 1e6) / (tokenReserves / 1e6); // Both 6 decimals
            }

            return price;

        } catch (error) {
            console.error(`❌ Error calculating PumpSwap price:`, error.message);
            return 0;
        }
    }

    /**
     * Get token balance from vault address
     */
    async getTokenBalance(vaultAddress) {
        try {
            const vaultPublicKey = new PublicKey(vaultAddress);
            const accountInfo = await this.rpcConnection.getAccountInfo(vaultPublicKey);

            if (!accountInfo || !accountInfo.data) {
                return 0;
            }

            // Parse token account data to get balance
            // Token account balance is stored at offset 64-72 (8 bytes, little endian)
            const balanceBuffer = Buffer.from(accountInfo.data.subarray(64, 72));
            const balance = balanceBuffer.readBigUInt64LE(0);

            return Number(balance);

        } catch (error) {
            console.error(`❌ Error getting token balance for ${vaultAddress}:`, error.message);
            return 0;
        }
    }

    /**
     * Calculate market cap
     */
    async calculateMarketCap(tokenMint, price) {
        try {
            const supplyData = await this.getTokenSupply(tokenMint);
            if (!supplyData) {
                console.log(`⚠️  Could not get token supply for market cap calculation`);
                return 0;
            }

            const marketCap = supplyData.totalSupply * price;
            console.log(`📊 Market Cap: $${marketCap.toLocaleString()} (Supply: ${supplyData.totalSupply.toLocaleString()} × Price: $${price.toFixed(6)})`);

            return marketCap;

        } catch (error) {
            console.error(`❌ Error calculating market cap:`, error.message);
            return 0;
        }
    }

    /**
     * Start real-time monitoring using gRPC Yellowstone
     */
    async startRealTimeMonitoring() {
        if (this.isMonitoring) {
            console.log(`⚠️  Real-time monitoring is already active`);
            return;
        }

        console.log(`🚀 Starting real-time price and market cap monitoring...`);

        try {
            // First, get initial price and market cap
            await this.updatePriceAndMarketCap();

            // Find liquidity pools to monitor
            const pools = await this.findLiquidityPools(this.tokenMintAddress);
            if (pools.length === 0) {
                console.log(`❌ No liquidity pools found for monitoring`);
                return;
            }

            // Set up gRPC subscription for pool account updates
            const stream = await this.grpcClient.subscribe();
            this.isMonitoring = true;

            // Subscribe to pool account updates
            const subscribeRequest = {
                accounts: {},
                slots: {},
                transactions: {},
                transactionsStatus: {},
                entry: {},
                blocks: {},
                blocksMeta: {},
                accountsDataSlice: [],
                commitment: 'processed'
            };

            // Add each pool to monitoring
            pools.forEach((pool, index) => {
                const accountKey = `pool_${index}`;

                if (pool.type === 'pumpfun') {
                    // Monitor the bonding curve account directly
                    subscribeRequest.accounts[accountKey] = {
                        account: [pool.address],
                        owner: [],
                        filters: [],
                        nonemptyTxnSignature: null
                    };
                } else if (pool.tokenAVault && pool.tokenBVault) {
                    // Monitor token vaults for AMM pools
                    subscribeRequest.accounts[accountKey] = {
                        account: [pool.tokenAVault, pool.tokenBVault],
                        owner: [],
                        filters: [],
                        nonemptyTxnSignature: null
                    };
                } else {
                    // Fallback: monitor the pool account itself
                    subscribeRequest.accounts[accountKey] = {
                        account: [pool.address],
                        owner: [],
                        filters: [],
                        nonemptyTxnSignature: null
                    };
                }
            });

            console.log(`📡 Subscribing to ${pools.length} liquidity pools for real-time updates...`);

            // Send subscription request with error handling
            try {
                stream.write(subscribeRequest);
                console.log(`✅ Subscription request sent successfully`);
            } catch (writeError) {
                console.error('❌ Failed to write subscription request:', writeError);
                this.isMonitoring = false;
                return;
            }

            // Handle incoming updates
            stream.on('data', async (data) => {
                try {
                    await this.handleAccountUpdate(data);
                } catch (dataError) {
                    console.error('❌ Error handling stream data:', dataError);
                }
            });

            stream.on('error', (error) => {
                console.error('❌ gRPC stream error:', error);
                console.log('🔄 Falling back to periodic price updates...');
                this.isMonitoring = false;
                this.startPeriodicUpdates();
            });

            stream.on('end', () => {
                console.log('📡 gRPC stream ended');
                console.log('🔄 Falling back to periodic price updates...');
                this.isMonitoring = false;
                this.startPeriodicUpdates();
            });

            // Keep connection alive with periodic pings
            this.startPingInterval(stream);

            console.log(`✅ Real-time monitoring started successfully`);

        } catch (error) {
            console.error('❌ Failed to start real-time monitoring:', error);
            this.isMonitoring = false;
        }
    }

    /**
     * Handle account updates from gRPC stream
     */
    async handleAccountUpdate(data) {
        try {
            if (data.account) {
                console.log(`🔄 Pool account updated, recalculating price...`);

                // Debounce updates to avoid excessive calculations
                clearTimeout(this.updateTimeout);
                this.updateTimeout = setTimeout(async () => {
                    await this.updatePriceAndMarketCap();
                }, 1000); // Wait 1 second before updating
            }
        } catch (error) {
            console.error('❌ Error handling account update:', error);
        }
    }

    /**
     * Update price and market cap
     */
    async updatePriceAndMarketCap() {
        try {
            console.log(`🔄 Updating price and market cap...`);

            // Find current liquidity pools
            const pools = await this.findLiquidityPools(this.tokenMintAddress);

            if (pools.length === 0) {
                console.log(`⚠️  No liquidity pools found for price calculation`);
                return;
            }

            // Calculate current price
            const price = await this.calculateTokenPrice(this.tokenMintAddress, pools);

            if (price > 0) {
                // Calculate market cap
                const marketCap = await this.calculateMarketCap(this.tokenMintAddress, price);

                // Update cache
                this.priceCache.set(this.tokenMintAddress, {
                    price: price,
                    timestamp: Date.now()
                });

                this.marketCapCache.set(this.tokenMintAddress, {
                    marketCap: marketCap,
                    timestamp: Date.now()
                });

                // Display results
                this.displayCurrentData(price, marketCap);
            }

        } catch (error) {
            console.error('❌ Error updating price and market cap:', error);
        }
    }

    /**
     * Display current price and market cap data
     */
    displayCurrentData(price, marketCap) {
        const timestamp = new Date().toLocaleTimeString();

        console.log(`\n📊 ===== REAL-TIME DATA UPDATE =====`);
        console.log(`⏰ Time: ${timestamp}`);
        console.log(`🪙 Token: ${this.tokenMintAddress}`);
        console.log(`💰 Price: $${price.toFixed(6)}`);
        console.log(`📈 Market Cap: $${marketCap.toLocaleString()}`);
        console.log(`=====================================\n`);
    }

    /**
     * Start ping interval to keep gRPC connection alive
     */
    startPingInterval(stream) {
        const PING_INTERVAL = 30000; // 30 seconds

        setInterval(async () => {
            if (!this.isMonitoring) return;

            const pingRequest = {
                ping: { id: Date.now() },
                slots: {},
                accounts: {},
                transactions: {},
                transactionsStatus: {},
                entry: {},
                blocks: {},
                blocksMeta: {},
                accountsDataSlice: [],
                commitment: 'processed'
            };

            try {
                stream.write(pingRequest);
            } catch (error) {
                console.error('❌ Ping failed:', error);
            }
        }, PING_INTERVAL);
    }

    /**
     * Get current cached price
     */
    getCurrentPrice(tokenMint = this.tokenMintAddress) {
        const cached = this.priceCache.get(tokenMint);
        if (cached && (Date.now() - cached.timestamp) < 60000) { // 1 minute cache
            return cached.price;
        }
        return null;
    }

    /**
     * Get current cached market cap
     */
    getCurrentMarketCap(tokenMint = this.tokenMintAddress) {
        const cached = this.marketCapCache.get(tokenMint);
        if (cached && (Date.now() - cached.timestamp) < 60000) { // 1 minute cache
            return cached.marketCap;
        }
        return null;
    }

    /**
     * Start periodic updates as fallback when gRPC fails
     */
    startPeriodicUpdates() {
        if (this.periodicInterval) {
            clearInterval(this.periodicInterval);
        }

        console.log(`🔄 Starting periodic price updates every 30 seconds...`);

        // Update immediately
        this.updatePriceAndMarketCap();

        // Then update every 30 seconds
        this.periodicInterval = setInterval(async () => {
            try {
                await this.updatePriceAndMarketCap();
            } catch (error) {
                console.error('❌ Error in periodic update:', error);
            }
        }, 30000); // 30 seconds

        console.log(`✅ Periodic updates started successfully`);
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        this.isMonitoring = false;

        if (this.periodicInterval) {
            clearInterval(this.periodicInterval);
            this.periodicInterval = null;
        }

        console.log(`🛑 Real-time monitoring stopped`);
    }
}

// Main execution function
async function main() {
    try {
        console.log(`🚀 Starting Real-Time Price & Market Cap Monitor`);
        console.log(`================================================`);

        const monitor = new RealTimePriceMarketCap();

        // Start real-time monitoring
        await monitor.startRealTimeMonitoring();

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log(`\n🛑 Shutting down gracefully...`);
            monitor.stopMonitoring();
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            console.log(`\n🛑 Shutting down gracefully...`);
            monitor.stopMonitoring();
            process.exit(0);
        });

        // Keep the process running
        console.log(`✅ Monitor is running. Press Ctrl+C to stop.`);

    } catch (error) {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    }
}

// Export the class for use in other modules
module.exports = RealTimePriceMarketCap;

// Run main function if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}