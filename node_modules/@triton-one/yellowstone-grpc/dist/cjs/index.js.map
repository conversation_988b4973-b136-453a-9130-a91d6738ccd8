{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,yCAKuB;AAGvB,wCAauB;AAGvB,wCA2BuB;AA1BrB,yGAAA,eAAe,OAAA;AACf,0GAAA,gBAAgB,OAAA;AAChB,2HAAA,iCAAiC,OAAA;AACjC,wHAAA,8BAA8B,OAAA;AAC9B,8HAAA,oCAAoC,OAAA;AACpC,oIAAA,0CAA0C,OAAA;AAC1C,sIAAA,4CAA4C,OAAA;AAC5C,sHAAA,4BAA4B,OAAA;AAC5B,0HAAA,gCAAgC,OAAA;AAChC,qHAAA,2BAA2B,OAAA;AAC3B,qHAAA,2BAA2B,OAAA;AAC3B,4HAAA,kCAAkC,OAAA;AAClC,wHAAA,8BAA8B,OAAA;AAC9B,sHAAA,4BAA4B,OAAA;AAC5B,0HAAA,gCAAgC,OAAA;AAChC,qHAAA,2BAA2B,OAAA;AAC3B,4HAAA,kCAAkC,OAAA;AAClC,yGAAA,eAAe,OAAA;AACf,gHAAA,sBAAsB,OAAA;AACtB,oHAAA,0BAA0B,OAAA;AAC1B,8GAAA,oBAAoB,OAAA;AACpB,kHAAA,wBAAwB,OAAA;AACxB,6GAAA,mBAAmB,OAAA;AACnB,6GAAA,mBAAmB,OAAA;AACnB,oHAAA,0BAA0B,OAAA;AAC1B,wHAAA,8BAA8B,OAAA;AAIhC,qFAAyE;AAO5D,QAAA,QAAQ,GAAG;IACtB,QAAQ,EAAE,IAAI,CAAC,yBAAyB;IACxC,UAAU,EAAE,IAAI,CAAC,SAAS;IAC1B,MAAM,EAAE,UACN,OAAuC,EACvC,QAAW,EACX,iCAAqD,EACrD,YAAqB;QAErB,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,SAAS,CACZ,uCAA8B,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EACvD,QAAQ,EACR,iCAAiC,EACjC,YAAY,CACb,CACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEW,QAAA,WAAW,GAAG;IACzB,UAAU,EAAE,IAAI,CAAC,eAAe;IAChC,MAAM,EAAE,UAAC,GAAe;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC;CACF,CAAC;AAEF;IAGE,gBACE,QAAgB,EAChB,MAA0B,EAC1B,cAA0C;QAE1C,IAAI,KAAyB,CAAC;QAE9B,IAAM,WAAW,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAC5B,IAAI,IAAI,IAAI,EAAE,EAAE;YACd,QAAQ,WAAW,CAAC,QAAQ,EAAE;gBAC5B,KAAK,QAAQ;oBACX,IAAI,GAAG,KAAK,CAAC;oBACb,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;aACT;SACF;QAGD,IAAI,WAAW,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACrC,KAAK,GAAG,qBAAW,CAAC,yBAAyB,CAC3C,qBAAW,CAAC,SAAS,EAAE,EACvB,qBAAW,CAAC,2BAA2B,CAAC,UAAC,OAAO,EAAE,QAAQ;gBACxD,IAAM,QAAQ,GAAG,IAAI,kBAAQ,EAAE,CAAC;gBAChC,IAAI,MAAM,KAAK,SAAS,EAAE;oBACxB,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;iBACjC;gBACD,OAAO,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAClC,CAAC,CAAC,CACH,CAAC;SACH;aAAM;YACL,KAAK,GAAG,4BAAkB,CAAC,cAAc,EAAE,CAAC;YAC5C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;aAC/B;SACF;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,qBAAY,CAC7B,UAAG,WAAW,CAAC,QAAQ,cAAI,IAAI,CAAE,EACjC,KAAK,EACL,cAAc,CACf,CAAC;IACJ,CAAC;IAEO,qCAAoB,GAA5B;QACE,IAAM,QAAQ,GAAG,IAAI,kBAAQ,EAAE,CAAC;QAChC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SAC/C;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEK,0BAAS,GAAf;;;;4BACS,WAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAA;4BAAhE,WAAO,SAAyD,EAAC;;;;KAClE;IAEK,8BAAa,GAAnB,UACE,QAA2D,EAC3D,KAAqD,EACrD,YAAmE,EACnE,kBAAyE,EACzE,KAAqD,EACrD,MAAuD,EACvD,UAA+D,EAC/D,UAAuC,EACvC,iBAAsD;;;;;4BAEvC,WAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAA;;wBAAlE,MAAM,GAAG,SAAyD;wBAExE,WAAM,IAAI,OAAO,CAAO,UAAC,OAAO,EAAE,MAAM;gCACtC,MAAM,CAAC,KAAK,CACV;oCACE,QAAQ,UAAA;oCACR,KAAK,OAAA;oCACL,YAAY,cAAA;oCACZ,kBAAkB,oBAAA;oCAClB,KAAK,OAAA;oCACL,MAAM,QAAA;oCACN,UAAU,YAAA;oCACV,UAAU,YAAA;oCACV,iBAAiB,mBAAA;iCAClB,EACD,UAAC,GAAQ;oCACP,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;wCACrC,OAAO,EAAE,CAAC;qCACX;yCAAM;wCACL,MAAM,CAAC,GAAG,CAAC,CAAC;qCACb;gCACH,CAAC,CACF,CAAC;4BACJ,CAAC,CAAC,EAAA;;wBArBF,SAqBE,CAAC;wBAEH,WAAO,MAAM,EAAC;;;;KACf;IAEK,qBAAI,GAAV,UAAW,KAAa;;;;;4BACf,WAAM,IAAI,OAAO,CAAS,UAAC,OAAO,EAAE,MAAM;4BAC/C,KAAI,CAAC,OAAO,CAAC,IAAI,CACf,EAAE,KAAK,OAAA,EAAE,EACT,KAAI,CAAC,oBAAoB,EAAE,EAC3B,UAAC,GAAG,EAAE,QAAQ;gCACZ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;oCACrC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iCACzB;qCAAM;oCACL,MAAM,CAAC,GAAG,CAAC,CAAC;iCACb;4BACH,CAAC,CACF,CAAC;wBACJ,CAAC,CAAC,EAAA;4BAZF,WAAO,SAYL,EAAC;;;;KACJ;IAEK,mCAAkB,GAAxB,UACE,UAA4B;;;;;4BAErB,WAAM,IAAI,OAAO,CAA6B,UAAC,OAAO,EAAE,MAAM;4BACnE,KAAI,CAAC,OAAO,CAAC,kBAAkB,CAC7B,EAAE,UAAU,YAAA,EAAE,EACd,KAAI,CAAC,oBAAoB,EAAE,EAC3B,UAAC,GAAG,EAAE,QAAQ;gCACZ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;oCACrC,OAAO,CAAC,QAAQ,CAAC,CAAC;iCACnB;qCAAM;oCACL,MAAM,CAAC,GAAG,CAAC,CAAC;iCACb;4BACH,CAAC,CACF,CAAC;wBACJ,CAAC,CAAC,EAAA;4BAZF,WAAO,SAYL,EAAC;;;;KACJ;IAEK,+BAAc,GAApB,UAAqB,UAA4B;;;;;4BACxC,WAAM,IAAI,OAAO,CAAS,UAAC,OAAO,EAAE,MAAM;4BAC/C,KAAI,CAAC,OAAO,CAAC,cAAc,CACzB,EAAE,UAAU,YAAA,EAAE,EACd,KAAI,CAAC,oBAAoB,EAAE,EAC3B,UAAC,GAAG,EAAE,QAAQ;gCACZ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;oCACrC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;iCAC/B;qCAAM;oCACL,MAAM,CAAC,GAAG,CAAC,CAAC;iCACb;4BACH,CAAC,CACF,CAAC;wBACJ,CAAC,CAAC,EAAA;4BAZF,WAAO,SAYL,EAAC;;;;KACJ;IAEK,wBAAO,GAAb,UAAc,UAA4B;;;;;4BACjC,WAAM,IAAI,OAAO,CAAS,UAAC,OAAO,EAAE,MAAM;4BAC/C,KAAI,CAAC,OAAO,CAAC,OAAO,CAClB,EAAE,UAAU,YAAA,EAAE,EACd,KAAI,CAAC,oBAAoB,EAAE,EAC3B,UAAC,GAAG,EAAE,QAAQ;gCACZ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;oCACrC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;iCACxB;qCAAM;oCACL,MAAM,CAAC,GAAG,CAAC,CAAC;iCACb;4BACH,CAAC,CACF,CAAC;wBACJ,CAAC,CAAC,EAAA;4BAZF,WAAO,SAYL,EAAC;;;;KACJ;IAEK,iCAAgB,GAAtB,UACE,SAAiB,EACjB,UAA4B;;;;;4BAErB,WAAM,IAAI,OAAO,CAA2B,UAAC,OAAO,EAAE,MAAM;4BACjE,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAC3B,EAAE,SAAS,WAAA,EAAE,UAAU,YAAA,EAAE,EACzB,KAAI,CAAC,oBAAoB,EAAE,EAC3B,UAAC,GAAG,EAAE,QAAQ;gCACZ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;oCACrC,OAAO,CAAC,QAAQ,CAAC,CAAC;iCACnB;qCAAM;oCACL,MAAM,CAAC,GAAG,CAAC,CAAC;iCACb;4BACH,CAAC,CACF,CAAC;wBACJ,CAAC,CAAC,EAAA;4BAZF,WAAO,SAYL,EAAC;;;;KACJ;IAEK,2BAAU,GAAhB;;;;;4BACS,WAAM,IAAI,OAAO,CAAS,UAAC,OAAO,EAAE,MAAM;4BAC/C,KAAI,CAAC,OAAO,CAAC,UAAU,CACrB,EAAE,EACF,KAAI,CAAC,oBAAoB,EAAE,EAC3B,UAAC,GAAG,EAAE,QAAQ;gCACZ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;oCACrC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;iCAC3B;qCAAM;oCACL,MAAM,CAAC,GAAG,CAAC,CAAC;iCACb;4BACH,CAAC,CACF,CAAC;wBACJ,CAAC,CAAC,EAAA;4BAZF,WAAO,SAYL,EAAC;;;;KACJ;IACH,aAAC;AAAD,CAAC,AAxMD,IAwMC"}