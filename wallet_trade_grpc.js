require('dotenv').config();
const { Connection, PublicKey, clusterApiUrl, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const Client = require('@triton-one/yellowstone-grpc').default;
const { CommitmentLevel } = require('@triton-one/yellowstone-grpc');

class YellowstoneWalletTradeAnalyzer {
    constructor() {
        this.walletAddress = process.env.WALLET_ADDRESS;
        this.grpcUrl = process.env.SOLANA_GRPC_URL || 'solana-yellowstone-grpc.publicnode.com:443';
        this.rpcUrl = process.env.SOLANA_RPC_URL || clusterApiUrl('mainnet-beta');

        // Initialize both gRPC and RPC connections
        this.grpcClient = new Client(`https://${this.grpcUrl}`, undefined, {
            'grpc.keepalive_time_ms': 30000,
            'grpc.keepalive_timeout_ms': 5000,
            'grpc.keepalive_permit_without_calls': true,
            'grpc.http2.max_pings_without_data': 0,
            'grpc.http2.min_time_between_pings_ms': 10000,
            'grpc.http2.min_ping_interval_without_data_ms': 300000
        });

        this.rpcConnection = new Connection(this.rpcUrl, 'confirmed');

        // Cache for token metadata
        this.tokenMetadataCache = new Map();

        // Storage for collected transactions
        this.collectedTransactions = [];
        this.signedTransactions = [];
        this.targetCount = 30;
        this.isCollecting = false;

        if (!this.walletAddress) {
            throw new Error('WALLET_ADDRESS not found in .env file');
        }

        try {
            this.publicKey = new PublicKey(this.walletAddress);
        } catch (error) {
            throw new Error(`Invalid wallet address format: ${this.walletAddress}`);
        }

        console.log(`🔗 gRPC Connected to: ${this.grpcUrl}`);
        console.log(`🔗 RPC Connected to: ${this.rpcUrl}`);
        console.log(`👛 Wallet Address: ${this.walletAddress}`);
    }

    /**
     * Get token metadata (reused from original implementation)
     */
    async getTokenMetadata(mintAddress) {
        if (this.tokenMetadataCache.has(mintAddress)) {
            return this.tokenMetadataCache.get(mintAddress);
        }

        try {
            if (mintAddress === 'So11111111111111111111111111111111111111112') {
                const metadata = {
                    mint: mintAddress,
                    symbol: 'SOL',
                    name: 'Solana',
                    decimals: 9
                };
                this.tokenMetadataCache.set(mintAddress, metadata);
                return metadata;
            }

            const mintPublicKey = new PublicKey(mintAddress);
            const mintInfo = await this.rpcConnection.getAccountInfo(mintPublicKey);

            let decimals = 0;
            if (mintInfo && mintInfo.data.length >= 44) {
                decimals = mintInfo.data[44];
            }

            const metadata = {
                mint: mintAddress,
                symbol: this.getTokenSymbolFromMint(mintAddress),
                name: this.getTokenNameFromMint(mintAddress),
                decimals: decimals
            };

            this.tokenMetadataCache.set(mintAddress, metadata);
            return metadata;

        } catch (error) {
            const fallbackMetadata = {
                mint: mintAddress,
                symbol: 'UNKNOWN',
                name: 'Unknown Token',
                decimals: 0
            };
            this.tokenMetadataCache.set(mintAddress, fallbackMetadata);
            return fallbackMetadata;
        }
    }

    getTokenSymbolFromMint(mintAddress) {
        const knownTokens = {
            'So11111111111111111111111111111111111111112': 'SOL',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
        };
        return knownTokens[mintAddress] || mintAddress.substring(0, 8) + '...';
    }

    getTokenNameFromMint(mintAddress) {
        const knownTokens = {
            'So11111111111111111111111111111111111111112': 'Solana',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USD Coin',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'Tether USD',
        };
        return knownTokens[mintAddress] || `Token ${mintAddress.substring(0, 8)}...`;
    }

    /**
     * Parse token transfers from transaction (reused from original)
     */
    async parseTokenTransfers(transaction) {
        const transfers = [];

        if (!transaction.meta || !transaction.meta.preTokenBalances || !transaction.meta.postTokenBalances) {
            return transfers;
        }

        const preBalances = transaction.meta.preTokenBalances;
        const postBalances = transaction.meta.postTokenBalances;
        const balanceChanges = new Map();

        // Process pre-balances
        for (const balance of preBalances) {
            const key = `${balance.accountIndex}-${balance.mint}`;
            if (!balanceChanges.has(key)) {
                balanceChanges.set(key, {
                    accountIndex: balance.accountIndex,
                    mint: balance.mint,
                    owner: balance.owner,
                    preAmount: parseFloat(balance.uiTokenAmount.amount),
                    postAmount: 0,
                    decimals: balance.uiTokenAmount.decimals
                });
            } else {
                balanceChanges.get(key).preAmount = parseFloat(balance.uiTokenAmount.amount);
            }
        }

        // Process post-balances
        for (const balance of postBalances) {
            const key = `${balance.accountIndex}-${balance.mint}`;
            if (!balanceChanges.has(key)) {
                balanceChanges.set(key, {
                    accountIndex: balance.accountIndex,
                    mint: balance.mint,
                    owner: balance.owner,
                    preAmount: 0,
                    postAmount: parseFloat(balance.uiTokenAmount.amount),
                    decimals: balance.uiTokenAmount.decimals
                });
            } else {
                balanceChanges.get(key).postAmount = parseFloat(balance.uiTokenAmount.amount);
            }
        }

        // Calculate changes
        for (const [, change] of balanceChanges) {
            const amount = change.postAmount - change.preAmount;
            if (amount !== 0) {
                const transfer = {
                    mint: change.mint,
                    amount: amount / Math.pow(10, change.decimals),
                    rawAmount: amount,
                    decimals: change.decimals,
                    type: amount > 0 ? 'received' : 'sent',
                    owner: change.owner,
                    isOwnerWallet: change.owner === this.walletAddress,
                    accountIndex: change.accountIndex
                };
                transfers.push(transfer);
            }
        }

        return transfers;
    }

    async parseSolTransfer(transaction) {
        if (!transaction.meta || !transaction.meta.preBalances || !transaction.meta.postBalances) {
            return null;
        }

        if (!transaction.transaction || !transaction.transaction.message || !transaction.transaction.message.accountKeys) {
            return null;
        }

        const accountKeys = transaction.transaction.message.accountKeys;
        let walletIndex = -1;

        for (let i = 0; i < accountKeys.length; i++) {
            if (accountKeys[i].toString() === this.walletAddress) {
                walletIndex = i;
                break;
            }
        }

        if (walletIndex === -1) return null;

        const preBalance = transaction.meta.preBalances[walletIndex];
        const postBalance = transaction.meta.postBalances[walletIndex];
        const change = (postBalance - preBalance) / LAMPORTS_PER_SOL;

        if (change === 0) return null;

        return {
            amount: Math.abs(change),
            type: change > 0 ? 'received' : 'sent',
            mint: 'So11111111111111111111111111111111111111112'
        };
    }

    /**
     * Analyze trade details (reused from original)
     */
    analyzeTradeDetails(tokenTransfers, solTransfer) {
        if (!tokenTransfers || tokenTransfers.length === 0) {
            if (solTransfer && Math.abs(solTransfer.amount) > 0.000001) {
                return {
                    tradeType: solTransfer.type === 'received' ? 'SOL_RECEIVE' : 'SOL_SEND',
                    buyTokenMint: solTransfer.type === 'received' ? 'So11111111111111111111111111111111111111112' : null,
                    sellTokenMint: solTransfer.type === 'sent' ? 'So11111111111111111111111111111111111111112' : null,
                    receivedTokens: [],
                    sentTokens: [],
                    solTransfer,
                    sourceWallet: this.walletAddress,
                    destinationWallet: null
                };
            }
            return null;
        }

        const walletReceivedTokens = tokenTransfers.filter(t => t.type === 'received' && t.isOwnerWallet);
        const walletSentTokens = tokenTransfers.filter(t => t.type === 'sent' && t.isOwnerWallet);
        const allReceivedTokens = tokenTransfers.filter(t => t.type === 'received');
        const allSentTokens = tokenTransfers.filter(t => t.type === 'sent');

        const hasSolReceived = solTransfer && solTransfer.type === 'received' && Math.abs(solTransfer.amount) > 0.000001;
        const hasSolSent = solTransfer && solTransfer.type === 'sent' && Math.abs(solTransfer.amount) > 0.000001;

        let tradeType = null;
        let buyTokenMint = null;
        let sellTokenMint = null;
        let destinationWallet = null;

        if (walletReceivedTokens.length > 0 && hasSolSent) {
            tradeType = 'BUY';
            buyTokenMint = walletReceivedTokens[0].mint;
            sellTokenMint = 'So11111111111111111111111111111111111111112';
            const tokenSender = allSentTokens.find(t => t.mint === buyTokenMint && !t.isOwnerWallet);
            destinationWallet = tokenSender ? tokenSender.owner : null;
        } else if (walletSentTokens.length > 0 && hasSolReceived) {
            tradeType = 'SELL';
            buyTokenMint = 'So11111111111111111111111111111111111111112';
            sellTokenMint = walletSentTokens[0].mint;
            const tokenReceiver = allReceivedTokens.find(t => t.mint === sellTokenMint && !t.isOwnerWallet);
            destinationWallet = tokenReceiver ? tokenReceiver.owner : null;
        } else if (walletReceivedTokens.length > 0 && walletSentTokens.length > 0) {
            if (walletReceivedTokens[0].mint !== walletSentTokens[0].mint) {
                tradeType = 'SWAP';
                buyTokenMint = walletReceivedTokens[0].mint;
                sellTokenMint = walletSentTokens[0].mint;
                const tokenReceiver = allReceivedTokens.find(t => t.mint === sellTokenMint && !t.isOwnerWallet);
                destinationWallet = tokenReceiver ? tokenReceiver.owner : null;
            }
        }

        if (buyTokenMint === sellTokenMint && buyTokenMint === 'So11111111111111111111111111111111111111112') {
            return null;
        }

        return {
            tradeType,
            buyTokenMint,
            sellTokenMint,
            sourceWallet: this.walletAddress,
            destinationWallet,
            receivedTokens: walletReceivedTokens,
            sentTokens: walletSentTokens,
            allReceivedTokens,
            allSentTokens,
            solTransfer
        };
    }

    /**
     * Hybrid approach: Use RPC for historical data (faster for bulk historical queries)
     * and gRPC for real-time monitoring
     */
    async getLatestSignedTransactionsHybrid(targetCount = 30) {
        const startTime = Date.now();
        console.log(`🚀 Fetching latest ${targetCount} signed transactions (HYBRID: RPC + gRPC)...`);
        console.log(`⏱️  Start time: ${new Date(startTime).toLocaleTimeString()}`);

        // Use RPC for historical data (more efficient for bulk queries)
        console.log(`📡 Using RPC for historical transaction retrieval...`);

        let signedTransactions = [];
        let before = null;
        let batchCount = 0;
        let totalProcessed = 0;
        const maxBatches = 50;
        const batchSize = 50;

        while (batchCount < maxBatches && signedTransactions.length < targetCount) {
            batchCount++;
            console.log(`   📦 RPC Batch ${batchCount}: Looking for signed transactions...`);

            const options = { limit: batchSize };
            if (before) {
                options.before = before;
            }

            const signatures = await this.rpcConnection.getSignaturesForAddress(
                this.publicKey,
                options
            );

            if (signatures.length === 0) {
                console.log(`   ✅ No more signatures found.`);
                break;
            }

            // Process signatures sequentially
            for (let i = 0; i < signatures.length; i++) {
                if (signedTransactions.length >= targetCount) break;

                const signatureInfo = signatures[i];
                try {
                    const transaction = await this.rpcConnection.getTransaction(signatureInfo.signature, {
                        maxSupportedTransactionVersion: 0,
                        commitment: 'confirmed'
                    });

                    if (!transaction) {
                        totalProcessed++;
                        continue;
                    }

                    // Quick signer check
                    let signer = null;
                    if (transaction.transaction && transaction.transaction.message) {
                        if ('accountKeys' in transaction.transaction.message) {
                            if (transaction.transaction.message.accountKeys && transaction.transaction.message.accountKeys.length > 0) {
                                signer = transaction.transaction.message.accountKeys[0].toString();
                            }
                        }
                    }

                    totalProcessed++;

                    // Only process if this wallet is the signer
                    if (signer === this.walletAddress) {
                        const tokenTransfers = await this.parseTokenTransfers(transaction);
                        const solTransfer = await this.parseSolTransfer(transaction);

                        const transactionDetails = {
                            signature: signatureInfo.signature,
                            blockTime: transaction.blockTime,
                            success: transaction.meta?.err === null,
                            signer,
                            tokenTransfers,
                            solTransfer,
                            logMessages: transaction.meta?.logMessages || [],
                            instructions: transaction.transaction?.message?.instructions?.length || 0
                        };

                        // Check if it's actually a trade
                        const tradeDetails = this.analyzeTradeDetails(tokenTransfers, solTransfer);
                        if (tradeDetails && tradeDetails.tradeType &&
                            ['BUY', 'SELL', 'SWAP'].includes(tradeDetails.tradeType)) {
                            signedTransactions.push(transactionDetails);
                        }
                    }

                    // Rate limiting
                    if (i % 5 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                } catch (error) {
                    console.log(`   ⚠️  Error checking transaction ${signatureInfo.signature}: ${error.message}`);
                    totalProcessed++;
                }

                if (totalProcessed % 10 === 0) {
                    console.log(`   🔄 Processed ${totalProcessed} transactions, found ${signedTransactions.length}/${targetCount} signed trades`);
                }
            }

            before = signatures[signatures.length - 1].signature;

            if (signatures.length < batchSize) {
                console.log(`   ✅ Reached end of transaction history.`);
                break;
            }

            await new Promise(resolve => setTimeout(resolve, 200));
        }

        const endTime = Date.now();
        const totalTime = endTime - startTime;
        const timeInSeconds = (totalTime / 1000).toFixed(2);
        const avgTimePerTransaction = totalProcessed > 0 ? (totalTime / totalProcessed).toFixed(0) : 0;

        console.log(`✅ Found ${signedTransactions.length} signed trade transactions out of ${totalProcessed} processed!`);
        console.log(`⏱️  Total time: ${timeInSeconds} seconds`);
        console.log(`⚡ Average time per transaction: ${avgTimePerTransaction}ms`);
        console.log(`🎯 Success rate: ${totalProcessed > 0 ? ((signedTransactions.length / totalProcessed) * 100).toFixed(1) : 0}% (${signedTransactions.length}/${totalProcessed})`);

        return signedTransactions;
    }

    /**
     * Real-time gRPC monitoring for new transactions
     */
    async startRealtimeMonitoring() {
        console.log(`🔴 Starting real-time gRPC monitoring for wallet: ${this.walletAddress}`);

        try {
            const stream = this.grpcClient.subscribe();

            // Handle incoming data
            stream.on('data', (data) => {
                this.handleGrpcData(data);
            });

            stream.on('error', (error) => {
                console.error('❌ gRPC Stream error:', error);
            });

            stream.on('end', () => {
                console.log('🔚 gRPC Stream ended');
            });

            // Subscribe to transactions mentioning our wallet
            const subscribeRequest = {
                slots: {},
                accounts: {},
                transactions: {
                    wallet_txs: {
                        vote: false,
                        failed: false,
                        accountInclude: [this.walletAddress]
                    }
                },
                blocks: {},
                blocksMeta: {},
                accountsDataSlice: [],
                commitment: CommitmentLevel.CONFIRMED
            };

            // Send subscription request
            await new Promise((resolve, reject) => {
                stream.write(subscribeRequest, (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            });

            console.log(`✅ gRPC subscription active for wallet transactions`);

            // Keep connection alive with pings
            this.startPingInterval(stream);

            return stream;

        } catch (error) {
            console.error('❌ Failed to start gRPC monitoring:', error);
            throw error;
        }
    }

    startPingInterval(stream) {
        const PING_INTERVAL = 30000; // 30 seconds

        setInterval(async () => {
            const pingRequest = {
                ping: { id: Date.now() },
                slots: {},
                accounts: {},
                transactions: {},
                blocks: {},
                blocksMeta: {},
                accountsDataSlice: []
            };

            try {
                await new Promise((resolve, reject) => {
                    stream.write(pingRequest, (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve();
                        }
                    });
                });
            } catch (error) {
                console.error('❌ Ping failed:', error);
            }
        }, PING_INTERVAL);
    }

    handleGrpcData(data) {
        if (data.pong) {
            console.log('🏓 Received pong from server');
            return;
        }

        if (data.transaction) {
            this.processRealtimeTransaction(data.transaction);
        }
    }

    async processRealtimeTransaction(transactionData) {
        try {
            const signature = transactionData.signature;
            console.log(`🔥 Real-time transaction detected: ${signature}`);

            // For real-time monitoring, you could process the transaction here
            // This is useful for immediate notifications of new trades

        } catch (error) {
            console.error('❌ Error processing real-time transaction:', error);
        }
    }

    /**
     * Display trade details (reused from original with modifications)
     */
    async displayTradeDetails(transaction, index) {
        const timestamp = transaction.blockTime ?
            new Date(transaction.blockTime * 1000).toLocaleString() : 'Unknown';

        const tradeDetails = this.analyzeTradeDetails(transaction.tokenTransfers, transaction.solTransfer);

        console.log(`\n📋 Transaction ${index + 1}`);
        console.log(`   🔗 Signature: ${transaction.signature}`);
        console.log(`   ⏰ Time: ${timestamp}`);
        console.log(`   ✅ Status: ${transaction.success ? 'Success' : 'Failed'}`);

        if (transaction.signer) {
            console.log(`   ✍️  Signer: ${transaction.signer}`);
            if (transaction.signer === this.walletAddress) {
                console.log(`   🔑 Signer Status: This is YOUR wallet (you signed this transaction)`);
            } else {
                console.log(`   🔑 Signer Status: External wallet signed this transaction`);
            }
        } else {
            console.log(`   ✍️  Signer: Unknown`);
        }

        if (tradeDetails && tradeDetails.tradeType) {
            const tradeEmoji = {
                'BUY': '🟢',
                'SELL': '🔴',
                'SWAP': '🔄',
                'SOL_RECEIVE': '💰',
                'SOL_SEND': '💸'
            };

            console.log(`   ${tradeEmoji[tradeDetails.tradeType] || '💫'} Trade Type: ${tradeDetails.tradeType}`);
            console.log(`   👤 Source Wallet: ${tradeDetails.sourceWallet}`);

            if (tradeDetails.destinationWallet) {
                console.log(`   🎯 Destination Wallet: ${tradeDetails.destinationWallet}`);
            } else {
                console.log(`   🎯 Destination Wallet: Unknown (complex transaction)`);
            }

            if (tradeDetails.buyTokenMint) {
                const buyTokenInfo = await this.getTokenMetadata(tradeDetails.buyTokenMint);
                console.log(`   📥 Buy Token: ${buyTokenInfo.symbol} (${buyTokenInfo.name})`);
                console.log(`   📥 Buy Token Mint: ${tradeDetails.buyTokenMint}`);
            }

            if (tradeDetails.sellTokenMint) {
                const sellTokenInfo = await this.getTokenMetadata(tradeDetails.sellTokenMint);
                console.log(`   📤 Sell Token: ${sellTokenInfo.symbol} (${sellTokenInfo.name})`);
                console.log(`   📤 Sell Token Mint: ${tradeDetails.sellTokenMint}`);
            }

            if (tradeDetails.receivedTokens.length > 0) {
                console.log(`   📈 Received:`);
                for (const token of tradeDetails.receivedTokens) {
                    const tokenInfo = await this.getTokenMetadata(token.mint);
                    console.log(`      ${token.amount.toFixed(6)} ${tokenInfo.symbol} (${tokenInfo.name})`);
                }
            }

            if (tradeDetails.sentTokens.length > 0) {
                console.log(`   📉 Sent:`);
                for (const token of tradeDetails.sentTokens) {
                    const tokenInfo = await this.getTokenMetadata(token.mint);
                    console.log(`      ${Math.abs(token.amount).toFixed(6)} ${tokenInfo.symbol} (${tokenInfo.name})`);
                }
            }

            if (tradeDetails.solTransfer) {
                const sol = tradeDetails.solTransfer;
                console.log(`   💎 SOL ${sol.type.toUpperCase()}: ${sol.amount.toFixed(6)} SOL`);
            }
        }
    }

    /**
     * Main run method for hybrid approach
     */
    async run(targetCount = 30, mode = 'hybrid') {
        const overallStartTime = Date.now();
        try {
            if (mode === 'realtime') {
                console.log(`🔴 Starting real-time gRPC monitoring mode...`);
                await this.startRealtimeMonitoring();
                console.log(`✅ Real-time monitoring started. Press Ctrl+C to stop.`);

                // Keep the process running
                process.on('SIGINT', () => {
                    console.log('\n🛑 Stopping real-time monitoring...');
                    process.exit(0);
                });

                // Keep alive
                setInterval(() => {
                    // Keep process alive
                }, 1000);

                return;
            }

            console.log(`🔍 Fetching latest ${targetCount} signed transactions (HYBRID MODE)...`);
            const signedTrades = await this.getLatestSignedTransactionsHybrid(targetCount);

            console.log('\n' + '='.repeat(80));
            console.log(`📊 YOUR LATEST ${targetCount} SIGNED TRADES (HYBRID: RPC + gRPC)`);
            console.log('='.repeat(80));

            if (signedTrades.length === 0) {
                console.log('📭 No signed trades found in recent wallet history.');
                console.log('💡 This might mean:');
                console.log('   - You haven\'t made any trades recently');
                console.log('   - Your trades are controlled by external wallets/bots');
                console.log('   - The wallet is used for receiving only');
                return;
            }

            // Calculate trade statistics
            const tradeStats = { BUY: 0, SELL: 0, SWAP: 0 };
            for (const transaction of signedTrades) {
                const tradeDetails = this.analyzeTradeDetails(transaction.tokenTransfers, transaction.solTransfer);
                if (tradeDetails && tradeDetails.tradeType) {
                    tradeStats[tradeDetails.tradeType]++;
                }
            }

            console.log(`🎯 Found ${signedTrades.length} signed trades:`);
            console.log(`   🟢 BUY: ${tradeStats.BUY} trades`);
            console.log(`   🔴 SELL: ${tradeStats.SELL} trades`);
            console.log(`   🔄 SWAP: ${tradeStats.SWAP} trades\n`);

            for (let i = 0; i < signedTrades.length; i++) {
                await this.displayTradeDetails(signedTrades[i], i);
            }

            const overallEndTime = Date.now();
            const overallTime = overallEndTime - overallStartTime;
            const overallTimeInSeconds = (overallTime / 1000).toFixed(2);

            console.log('\n' + '='.repeat(80));
            console.log(`✅ ${signedTrades.length} YOUR SIGNED trades displayed!`);
            console.log(`📊 Your Trade Summary: ${tradeStats.BUY} BUY | ${tradeStats.SELL} SELL | ${tradeStats.SWAP} SWAP`);
            console.log(`⚡ HYBRID MODE: RPC for historical + gRPC for real-time capability`);
            console.log(`⏱️  TOTAL EXECUTION TIME: ${overallTimeInSeconds} seconds`);

        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    }
}

// Run the application if called directly
if (require.main === module) {
    const analyzer = new YellowstoneWalletTradeAnalyzer();

    // Check command line arguments
    const args = process.argv.slice(2);
    let targetCount = 30;
    let mode = 'hybrid'; // 'hybrid' or 'realtime'

    // Show help if requested
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
🚀 YELLOWSTONE gRPC WALLET TRANSACTION ANALYZER
===============================================

USAGE:
  node wallet_trade_grpc.js [OPTIONS]

OPTIONS:
  --count, -c <number>    Number of signed transactions to fetch (default: 30)
  --mode, -m <mode>       Mode: 'hybrid' or 'realtime' (default: hybrid)
  --help, -h              Show this help message

MODES:
  hybrid                  Use RPC for historical data + gRPC capability (FAST)
  realtime               Real-time gRPC monitoring of new transactions

EXAMPLES:
  node wallet_trade_grpc.js                           # Get latest 30 signed transactions (hybrid)
  node wallet_trade_grpc.js --count 10                # Get latest 10 signed transactions
  node wallet_trade_grpc.js --mode realtime           # Start real-time monitoring
  node wallet_trade_grpc.js -c 50 -m hybrid          # Get 50 transactions in hybrid mode

FEATURES:
  ⚡ Hybrid approach: RPC for historical + gRPC for real-time
  ⚡ Real-time monitoring: Live transaction notifications
  ⚡ Smart filtering: Only signed transactions
  ⚡ Performance timing: Detailed execution metrics

PERFORMANCE:
  🟢 HYBRID: Fast historical retrieval + real-time capability
  🟡 REALTIME: Live monitoring (runs continuously)
        `);
        process.exit(0);
    }

    // Check for --count or -c argument
    const countIndex = args.findIndex(arg => arg === '--count' || arg === '-c');
    if (countIndex !== -1 && args[countIndex + 1]) {
        const parsedCount = parseInt(args[countIndex + 1]);
        if (!isNaN(parsedCount) && parsedCount > 0) {
            targetCount = parsedCount;
        }
    }

    // Check for --mode or -m argument
    const modeIndex = args.findIndex(arg => arg === '--mode' || arg === '-m');
    if (modeIndex !== -1 && args[modeIndex + 1]) {
        const parsedMode = args[modeIndex + 1].toLowerCase();
        if (['hybrid', 'realtime'].includes(parsedMode)) {
            mode = parsedMode;
        }
    }

    if (mode === 'realtime') {
        console.log(`🚀 Starting Yellowstone gRPC real-time monitoring...`);
        analyzer.run(targetCount, mode);
    } else {
        console.log(`🚀 Running Yellowstone gRPC hybrid analysis for latest ${targetCount} signed transactions...`);
        analyzer.run(targetCount, mode);
    }
}

module.exports = YellowstoneWalletTradeAnalyzer;